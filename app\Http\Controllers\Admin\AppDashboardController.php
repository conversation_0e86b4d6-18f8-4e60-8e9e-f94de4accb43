<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\AppDashboard;
use App\Models\AppDashboardService;
use App\Models\AppDashboardServiceOption;
use App\Models\Location;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use File;
use Illuminate\Support\Str;

class AppDashboardController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function index(Request $request)
    {
        $data = array();
        $query = AppDashboard::query();

        if ($request->filled('status')) {
            $query->where('status', '=', $request->input('status'));
            $data['status'] = $request->input('status');
        }

        if ($request->filled('search')) {
            $query->where('name', 'LIKE', '%' . $request->input('search') . '%');
            $data['search'] = $request->input('search');
        }

        $app_dashboards = $query->paginate(25);
        $app_dashboards->appends($request->except('page'));

        return view('app_dashboards.index', compact('app_dashboards', 'data'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        $locations = Location::query()->whereIn('id', [664, 629, 375])->get();
        return view('app_dashboards.create', compact('locations'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     * @throws \Illuminate\Validation\ValidationException
     */
    public function store(Request $request)
    {
        $all_rules = [
            'name' => 'required',
            'title' => 'required',
            'location_id' => 'required',
            'priority' => 'required',
            'dashboard_icon' => 'required|file',
            'service_name' => 'required|min:1|array',
            'service_title' => 'required|min:1|array',
            'service_priority' => 'required|min:1|array',
            'service_icon' => 'required|min:1|array',
            'service_name.*' => 'required',
            'service_title.*' => 'required',
            'service_priority.*' => 'required',
            'service_icon.*' => 'required|file',
        ];

        $option_rules = $option_messages = [];
        for ($i = 0; $i < count($request['service_name']); $i++) {
            for ($j = 0; $j < count($request['option_url']['service_' . $i]); $j++) {
                $option_rules = [
                    'option_url.service_' . $i . '.' . $j => 'required',
                    'option_title.service_' . $i . '.' . $j => 'required',
                    'option_text.service_' . $i . '.' . $j => 'required',
                ];
                $option_messages = [
                    'option_url.service_' . $i . '.' . $j . '.required' => 'Option URL field is required',
                    'option_title.service_' . $i . '.' . $j . '.required' => 'Option Title field is required',
                    'option_text.service_' . $i . '.' . $j . '.required' => 'Option Text field is required',
                ];
            }
        }

        $all_messages = [
            'name.required' => 'The Name field is required',
            'title.required' => 'The Title field is required',
            'priority.required' => 'The Priority field is required',
            'location_id.required' => 'The Location field is required',
            'service_name.*.required' => 'The Service Name field is required',
            'service_title.*.required' => 'The Service Title field is required',
            'service_icon.*.required' => 'The Service Icon field is required',
            'service_priority.*.required' => 'The Service Priority field is required',
        ];

        $this->validate($request, array_merge($all_rules, $option_rules), array_merge($all_messages, $option_messages));

        $data = $request->all();

        if ($request->hasFile('dashboard_icon')) {
            $file = $request->file('dashboard_icon');
            $file->store('app-dashboards', 'public');
            $data['icon'] = str_replace('admin-', '', asset('storage/app/public/app-dashboards/' . $file->hashName()));
        }

        $status = 0;
        if (isset($data['status'])) {
            $data['status'] = 1;
        }

        $data['status'] = $status;

        $special = 0;
        if (isset($data['special'])) {
            $special = 1;
        }

        $data['special'] = $special;

        $app_dashboard = AppDashboard::query()->create([
            'name' => $data['name'],
            'status' => $data['status'],
            'icon' => isset($data['icon']) ? $data['icon'] : '',
            'title' => $data['title'],
            'priority' => $data['priority'],
            'special' => $data['special'],
            'location_id' => $data['location_id']
        ]);

        for ($i = 0; $i < count($data['service_name']); $i++) {
            if ($request->file('service_icon')[$i]) {
                $file = $request->file('service_icon')[$i];
                $file->store('app-dashboards/services', 'public');
                $data['service_icon'][$i] = str_replace('admin-', '', asset('storage/app/public/app-dashboards/services/' . $file->hashName()));
            }
            $status = 0;
            if (isset($data['service_status'][$i])) {
                $status = 1;
            }

            $data['service_status'][$i] = $status;
            $app_dashboard_service = AppDashboardService::query()->create([
                'app_dashboard_id' => $app_dashboard->id,
                'icon' => $data['service_icon'][$i],
                'name' => $data['service_name'][$i],
                'title' => $data['service_title'][$i],
                'priority' => $data['service_priority'][$i],
                'status' => $data['service_status'][$i],
            ]);

            if ($app_dashboard_service) {
                for ($j = 0; $j < count($data['option_url']['service_' . $i]); $j++) {
                    AppDashboardServiceOption::query()->create([
                        'app_dashboard_service_id' => $app_dashboard_service->id,
                        'url' => $data['option_url']['service_' . $i][$j],
                        'title' => $data['option_title']['service_' . $i][$j],
                        'text' => $data['option_text']['service_' . $i][$j],
                        'priority' => $j,
                    ]);
                }
            }
        }

        if ($app_dashboard) {
            session()->flash('alert-success', 'App Dashboard added successfully.');
            return redirect()->route('app-dashboards.index');
        } else {
            session()->flash('alert-danger', 'There was error in adding App Dashboard, please try again.');
            return redirect()->route('app-dashboards.create');
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  \App\Models\AppDashboard  $appDashboard
     * @return \Illuminate\Http\Response
     */
    public function show(AppDashboard $appDashboard)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  \App\Models\AppDashboard  $appDashboard
     * @return \Illuminate\Http\Response
     */
    public function edit(AppDashboard $appDashboard)
    {
        $locations = Location::query()->whereIn('id', [664, 629, 375])->get();
        return view('app_dashboards.edit', compact('appDashboard', 'locations'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \App\Models\AppDashboard  $appDashboard
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, AppDashboard $appDashboard)
    {
        $all_rules = [
            'name' => 'required',
            'title' => 'required',
            'location_id' => 'required',
            'priority' => 'required',
            'service_name' => 'required|min:1|array',
            'service_title' => 'required|min:1|array',
            'service_priority' => 'required|min:1|array',
            'service_name.*' => 'required',
            'service_title.*' => 'required',
            'service_priority.*' => 'required',
        ];

        $data = $request->all();
        foreach ($data as $key => $datum) {
            if (Str::contains($key, ['option_url', 'option_text', 'option_title'])) {
                if (is_array($datum)) {
                    $i = 0;
                    unset($data[$key]);
                    foreach ($datum as $k => $option) {
                        $data[$key]['service_' . $i] = $option;
                        $i++;
                    }
                }
            } else {
                if (is_array($datum)) {
                    $data[$key] = array_values($datum);
                }
            }
        }

        $all_messages = [
            'name.required' => 'The Name field is required',
            'title.required' => 'The Title field is required',
            'priority.required' => 'The Priority field is required',
            'location_id.required' => 'The Location field is required',
            'service_name.*.required' => 'The Service Name field is required',
            'service_title.*.required' => 'The Service Title field is required',
            'service_priority.*.required' => 'The Service Priority field is required',
        ];

        $this->validate($request, $all_rules, $all_messages);

        if ($request->hasFile('dashboard_icon')) {
            $file = $request->file('dashboard_icon');
            $file->store('app-dashboards', 'public');
            $data['icon'] = str_replace('admin-', '', asset('storage/app/public/app-dashboards/' . $file->hashName()));
        }

        $status = 0;
        if (isset($data['status'])) {
            $status = 1;
        }

        $data['status'] = $status;

        $special = 0;
        if (isset($data['special'])) {
            $special = 1;
        }

        $data['special'] = $special;

        $appDashboard->update([
            'name' => $data['name'],
            'status' => $data['status'],
            'icon' => isset($data['icon']) ? $data['icon'] : '',
            'title' => $data['title'],
            'priority' => $data['priority'],
            'special' => $data['special'],
            'location_id' => $data['location_id']
        ]);

        foreach ($appDashboard->services as $key => $service) {
            $data['service_icon'][$key] = $service->icon;
        }

        $appDashboard->services()->delete();

        for ($i = 0; $i < count($data['service_name']); $i++) {
            $service_icon = @$request->file('service_icon')[$i];
            if (isset($service_icon)) {
                $service_icon->store('app-dashboards/services', 'public');
                $data['service_icon'][$i] = str_replace('admin-', '', asset('storage/app/public/app-dashboards/services/' . $service_icon->hashName()));
            }

            $app_dashboard_service = AppDashboardService::query()->create([
                'app_dashboard_id' => $appDashboard->id,
                'icon' => $data['service_icon'][$i],
                'name' => $data['service_name'][$i],
                'title' => $data['service_title'][$i],
                'priority' => $data['service_priority'][$i],
                'status' => $data['checkbox_service_status'][$i],
            ]);

            if ($app_dashboard_service) {
                $app_dashboard_service->options()->delete();
                for ($j = 0; $j < count($data['option_url']['service_' . $i]); $j++) {
                    AppDashboardServiceOption::query()->create([
                        'app_dashboard_service_id' => $app_dashboard_service->id,
                        'url' => $data['option_url']['service_' . $i][$j],
                        'title' => $data['option_title']['service_' . $i][$j],
                        'text' => $data['option_text']['service_' . $i][$j],
                        'priority' => $j,
                    ]);
                }
            }
        }

        if ($appDashboard) {
            session()->flash('alert-success', 'App Dashboard updated successfully.');
            return redirect()->route('app-dashboards.index');
        } else {
            session()->flash('alert-danger', 'There was error in updating App Dashboard, please try again.');
            return redirect()->route('app-dashboards.edit');
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  \App\Models\AppDashboard  $appDashboard
     * @return \Illuminate\Http\Response
     */
    public function destroy(AppDashboard $appDashboard)
    {
        if ($appDashboard->icon) {
            $imagePath = $appDashboard->icon;
            if (\Illuminate\Support\Facades\File::exists($imagePath)) {
                unlink($imagePath);
            }
        }

        $appDashboard->services()->delete();
        $appDashboard->delete();
        session()->flash('alert-info', 'App Dashboard Detail deleted successfully');
        return redirect()->route('app-dashboards.index');
    }

    public function getDetails(Request $request)
    {
        $location_id = $request->get('location_id');

        $app_dashboard = AppDashboard::query()
            ->with(['services' => function ($q) {
                $q->where('status', 1)
                    ->orderBy('priority');
            }, 'services.options' => function ($sub) {
                $sub->where('status', 1);
            }])
            ->where('location_id', $location_id)
            ->where('status', 1)
            ->orderBy('priority', 'ASC')
            ->get();

        return response()->json(['version' => 55, 'data' => $app_dashboard]);
    }

    public function getDetails4V2(Request $request)
    {

        $location_id = $request->get('location_id');

        $app_dashboard = AppDashboard::query()
            ->with(['services' => function ($q) {
                $q->where('status', 1)->orderBy('priority');
            }, 'services.options' => function ($sub) {
                $sub->where('status', 1);
            }])
            ->where('location_id', $location_id)
            ->where('status', 1)
            ->orderBy('priority', 'ASC')
            ->get();

        // Modify the structure to add `route` field
        $app_dashboard->each(function ($dashboard) {
            $dashboard->services->each(function ($service) {
                $service->options->transform(function ($option) use ($service) {
                    // if ($service->title === 'Domicile' && $option->title === 'Apply') {
                    //     $option->route = '/apply-domicile';
                    // } elseif ($service->title === 'Domicile' && $option->title === 'Verify') {
                    //     $option->route = '/domicile-verify';
                    if ($service->title === 'Pay Token & Challan Fee' && $option->title === 'Excise - Token Tax') {
                        $option->route = '/excise-vehical-no';
                    } elseif ($service->title === 'Pay Token & Challan Fee' && $option->title === 'Excise - Transfer Challan') {
                        $option->route = '/transfer-challan';
                    } elseif ($service->title === 'Pay Token & Challan Fee' && $option->title === 'Excise - Registration Challan') {
                        $option->route = '/transfer-challan';
                     } elseif ($service->title === 'View Challans' && $option->title === 'View Token Tax') {
                        $option->route = '/transfer-challan-listing';
                    } elseif ($service->title === 'View Challans' && $option->title === 'View Transfer Challan') {
                        $option->route = '/transfer-challan-listing';
                    } elseif ($service->title === 'View Challans' && $option->title === 'View Registration Challan') {
                        $option->route = '/transfer-challan-listing';
                    } else {
                        $option->route = '/webview';
                    }
                    return $option;
                });
            });
        });

        return response()->json(['version' => 55, 'data' => $app_dashboard]);
    }
}
