<?php

namespace App\Helpers;

use App\Models\Location;
use App\Models\Setting;
use GuzzleHttp\Client;
use Illuminate\Http\Request;

class Helper
{
    public static function add_setting_meta($meta_key, $meta_value)
    {
        return  Setting::create([
            'meta_key' => $meta_key,
            'meta_value' => $meta_value
        ]);
    }

    public static function update_setting_meta($meta_key, $meta_value)
    {

        return  Setting::updateOrCreate(
            ['meta_key' => $meta_key],
            ['meta_value' => $meta_value]
        );
    }

    public static function get_setting_meta($meta_key, $array = false)
    {

        $query = Setting::where('meta_key', $meta_key);

        if (!$array) {
            $setting = $query->first();
        } else {
            $setting = $query->get();
        }

        return $setting->meta_value ?? '';
    }

    public static function is_production()
    {
        return env('APP_ENV') === 'production';
    }

    public static function is_staging()
    {
        return env('APP_ENV') === 'staging';
    }

    public static function is_local()
    {
        return env('APP_ENV') === 'local';
    }

    public static function getCustomAccessToken()
    {
        try {
            $client = new Client();
            $payload = [
                "Data" => [
                    "User" => [
                        "username" => "nitb",
                        "password" => "nitb@543#"
                    ]
                ]
            ];
            $response = $client->post('http://119.159.244.2:4321/ut/api/utuser/V2/login', [
                'verify' => false,
                'json' => $payload,
            ]);

            $body = $response->getBody();
            $result = json_decode($body, true);
            return $result['Data']['Session']['token'];
        } catch (\Exception $e) {
            return null;
        }
    }
    public static function balochistanSync()
    {
        try {
            $client = new Client();
            $response = $client->post('https://policepkm.balochistanpolice.gov.pk/api/public_character/syc_verif_track', [
                'sec_key' => 'cs3pqO4xW6zFsRH5kaLomFPJHsHdQrWe',
            ]);

            $body = $response->getBody();
            $result = json_decode($body, true);
            return $result['data'];
        } catch (\Exception $e) {
            return null;
        }
    }
    public static function balochistanVerification(Request $request)
    {
        try {
            $client = new Client();
            $response = $client->post('https://policepkm.balochistanpolice.gov.pk/api/public_character/verify_doc', [
                'form_params' => [
                    'imei' => '33423423',
                    'device_type' => 'ios',
                    'vender_id' => '34234',
                    'latitude' => '32423423',
                    'longitude' => '*********',
                    'app_type' => '' . $request->input('app_type'),
                    'cnic' => '' . $request->input('cnic'),
                    'passport' => '' . $request->input('passport'),
                    'psc_number' => '' . $request->input('psc_number'),
                    'district_id' => '' . $request->input('district_id'),
                    'issued_date' => '' . date('d/m/Y', strtotime($request->input('issued_date'))),
                    'proceeding_to' => '' . $request->input('proceeding_to'),
                    'track' => '0',
                    'security_key' => '*********',
                    'version' => '0.1',
                ]
            ]);

            $body = $response->getBody();
            $result = json_decode($body, true);
            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }
    public static function balochistanTracking(Request $request)
    {
        try {
            $client = new Client();
            $response = $client->post('https://policepkm.balochistanpolice.gov.pk/api/public_character/verify_doc', [
                'form_params' => [
                    'imei' => '33423423',
                    'device_type' => 'ios',
                    'vender_id' => '34234',
                    'latitude' => '32423423',
                    'longitude' => '*********',
                    'app_type' => '' . $request->input('app_type'),
                    'cnic' => '' . $request->input('cnic'),
                    'passport' => '' . $request->input('passport'),
                    'district_id' => '' . $request->input('district_id'),
                    'proceeding_to' => '' . $request->input('proceeding_to'),
                    'track' => '1',
                    'security_key' => '*********',
                    'version' => '0.1',
                ]
            ]);

            $body = $response->getBody();
            $result = json_decode($body, true);
            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }
    public static function getCrimeReport(Request $request)
    {

        try {
            $client = new Client();
            $response = $client->get('https://cms.balochistanpolice.gov.pk/contract/api/public/complaints/' . $request->mob_num, [
                'headers' => [
                    'Content-type' => 'application/json',
                    'X-Secret' => '02DCEFE57CF14E3BB8F8678E36594488',
                ],
                'auth' => [
                    'qtacityapp',
                    'PKER32CFkuzgd6f(',
                ],
            ]);

            $body = $response->getBody();

            $result = json_decode($body, true);

            return $result['result'];
        } catch (\Exception $e) {
            return null;
        }
    }
    public static function getCrimeReportCategories()
    {

        try {
            $client = new Client();
            $response = $client->post('https://cms.balochistanpolice.gov.pk/contract/api/public/sync/category', [
                'headers' => [
                    'Content-type' => 'application/json',
                    'X-Secret' => '02DCEFE57CF14E3BB8F8678E36594488',
                ],
                'auth' => [
                    'qtacityapp',
                    'PKER32CFkuzgd6f(',
                ],
            ]);

            $body = $response->getBody();

            $result = json_decode($body, true);

            return $result['result'];
        } catch (\Exception $e) {
            return null;
        }
    }
    public static function registerComplaint(Request $request)
    {

        if ($request->firPs != null) {
            $firPs = (int)$request->firPs;
        } else {
            $firPs = null;
        }

        try {
            $client = new Client();
            $response = $client->post('https://cms.balochistanpolice.gov.pk/contract/api/public/complaints', [
                'headers' => [
                    'Content-Type' => 'application/json',
                    'X-Secret' => '02DCEFE57CF14E3BB8F8678E36594488',
                ],
                'auth' => [
                    'qtacityapp',
                    'PKER32CFkuzgd6f(',
                ],
                'body' => json_encode([
                    "districtId" => (int)$request->districtId,
                    "complainantPs" => (int)$request->complainantPs,
                    "categoryId" => (int)$request->categoryId,
                    "firPs" => $firPs,
                    "firNo" => $request->firNo,
                    "complainantName" => $request->complainantName,
                    "fatherGuardianName" => $request->fatherGuardianName,
                    "complainantGender" => (int)$request->complainantGender,
                    "isPakistani" => true,
                    "nationalIdentity" => $request->nationalIdentity,
                    "contactNo" => $request->contactNo,
                    "incidentDate" => $request->incidentDate,
                    "incidentDetail" => 'incident detials',
                    "incidentLocation" => $request->incidentLocation
                ])
            ]);

            $body = $response->getBody();

            $result = json_decode($body, true);

            return $result;
        } catch (\Exception $e) {
            return null;
        }
    }

    public static function getAreas()
    {
        $query = Location::where('location_level_id', 6)->get();
        return $query;
    }
    public static function getUC($id)
    {
        //For Area
        $query = Location::find($id);
        //For Ward
        $query = Location::find($query->parent_id);
        $ward = $query->name;
        //For UC
        $query = Location::find($query->parent_id);
        return $query->name . ' (' . $ward . ')';
    }
    public static function getTehsil($id)
    {
        //For Area
        $query = Location::find($id);
        //For Ward
        $query = Location::find($query->parent_id);
        $ward = $query->name;
        //For UC
        $query = Location::find($query->parent_id);
        //For Tehsil
        $query = Location::find($query->parent_id);
        return $query;
    }

    public static function formatNumber($number)
    {
        if ($number >= 1000000000) {
            return number_format($number / 1000000000, 1) . 'B'; // For billions
        } elseif ($number >= 1000000) {
            return number_format($number / 1000000, 1) . 'M'; // For millions
        } elseif ($number >= 1000) {
            return number_format($number / 1000, 1) . 'K'; // For thousands
        }
        return $number; // For numbers less than 1000, return the number as is
    }

    public static function generatePatternedToken(): string
    {
        return '#_' . bin2hex(random_bytes(16)); // 16 bytes = 32 hex characters
    }
}
