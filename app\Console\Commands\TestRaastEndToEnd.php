<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\RaastTransaction;
use App\Services\RaastIntegrationService;
use App\Services\XmlDigitalSignatureService;
use GuzzleHttp\Client;
use App\Traits\RaastTokenRefresh;


class TestRaastEndToEnd extends Command
{
    use RaastTokenRefresh;
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'raast:test-e2e
                            {--type=all : Test type (payment|rtp|rtp-only|pay-now-comprehensive|all)}
                            {--url=http://localhost : Base URL for testing}
                            {--count=1 : Number of test transactions}
                            {--detailed : Show detailed output}
                            {--real-integration : Use real CAS integration (requires working certificates)}
                            {--skip-customer-registration : Skip customer registration for RTP testing}
                            {--test-jwt : Test JWT token generation and validation}
                            {--test-scenarios : Run comprehensive Pay Now RTP test scenarios}
                            {--test-amount-modification : Test amount modification scenarios}
                            {--test-expiration : Test RTP expiration scenarios}
                            {--test-status-requests : Test RTP status request scenarios}
                            {--test-error-flows : Test unsuccessful flow scenarios}
                            {--test-signature : Test XML digital signature functionality}
                            {--include-signature : Include digital signature in generated XML (default: true)}
                            {--validate-signature : Validate signature structure in generated XML}
                            {--test-connectivity : Test network connectivity to RAAST endpoint}
                            {--test-auth : Test RAAST API authentication}
                            {--generate-client-token : Generate a sample RAAST client token}
                            {--generate-access-token : Generate a sample RAAST access token}
                            {--test-json-handling : Test non-JSON response handling}
                            {--test-xml-responses : Test XML response handling scenarios}
                            {--test-trace-consistency : Test trace reference consistency across operations}
                            {--test-production-rtp-only : Test production RTP-only functionality}
                            {--test-rtp-only-production : Test production RTP-only method with OAuth2 refresh}
                            {--debug : Enable debug mode with detailed response logging}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test RAAST payment system end-to-end process with real API integration and RTP-only (Pay Now) support';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $this->info('🚀 RAAST End-to-End Testing Started');
        $this->info('=' . str_repeat('=', 50));

        $type = $this->option('type');
        $verbose = $this->option('detailed') || $this->option('verbose') || $this->option('debug');
        $count = (int) $this->option('count');
        $useRealIntegration = $this->option('real-integration');
        $skipCustomerRegistration = false;
        $testJwt = $this->option('test-jwt');

        // Test system prerequisites
        $this->testPrerequisites($testJwt);

        if ($this->option('skip-customer-registration')) {
            $skipCustomerRegistration = true;
        }

        // Generate access token if requested
        // if ($this->option('generate-access-token')) {
        //     $this->generateAccessToken();
        //     return 0;
        // }

        // Generate access token if requested
        if ($this->option('generate-access-token')) {
            $this->generateAccessToken();
            return 0;
        }

        // Generate client token if requested
        if ($this->option('generate-client-token')) {
            $this->generateClientToken();
            return 0;
        }

        // Run authentication testing if requested
        if ($this->option('test-auth')) {
            $this->testRaastAuthentication($verbose);
            return 0;
        }

        // Run connectivity testing if requested
        if ($this->option('test-connectivity')) {
            $this->testRaastConnectivity($verbose);
            return 0;
        }

        // Run signature testing if requested
        if ($this->option('test-signature')) {
            $this->testXmlDigitalSignature($verbose);
            return 0;
        }

        // Run trace reference consistency testing if requested
        if ($this->option('test-trace-consistency')) {
            return $this->testTraceReferenceConsistency();
        }

        // Test production RTP-only functionality if requested
        if ($this->option('test-production-rtp-only')) {
            return $this->testProductionRtpOnlyFunctionality();
        }

        // Test production RTP-only method with OAuth2 refresh if requested
        if ($this->option('test-rtp-only-production')) {
            return $this->testRtpOnlyProductionMethod($verbose, $useRealIntegration);
        }

        // Run comprehensive test scenarios if requested
        if ($this->option('test-scenarios')) {
            $this->testComprehensivePayNowScenarios($verbose, $useRealIntegration);
            return 0;
        }

        // Run specific scenario tests
        if ($this->option('test-amount-modification')) {
            $this->testAmountModificationScenarios($verbose, $useRealIntegration);
        }
        if ($this->option('test-expiration')) {
            $this->testExpirationScenarios($verbose, $useRealIntegration);
        }
        if ($this->option('test-status-requests')) {
            $this->testStatusRequestScenarios($verbose, $useRealIntegration);
        }
        if ($this->option('test-error-flows')) {
            $this->testErrorFlowScenarios($verbose, $useRealIntegration);
        }

        if ($this->option('test-json-handling')) {
            $this->testNonJsonResponseHandling();
            return 0;
        }

        if ($this->option('test-xml-responses')) {
            $this->testXmlResponseHandling();
            return 0;
        }

        // Run tests based on type
        switch ($type) {
            case 'payment':
                $this->testPaymentFlow($count, $verbose, $useRealIntegration, $skipCustomerRegistration);
                break;
            case 'rtp':
                $this->testRtpFlow($count, $verbose, $useRealIntegration, $skipCustomerRegistration);
                break;
            case 'rtp-only':
                $this->testRtpOnlyFlow($count, $verbose, $useRealIntegration);
                break;
            case 'pay-now-comprehensive':
                $this->testComprehensivePayNowScenarios($verbose, $useRealIntegration);
                break;
            case 'all':
            default:
                $this->testPaymentFlow($count, $verbose, $useRealIntegration, $skipCustomerRegistration);
                $this->testRtpFlow($count, $verbose, $useRealIntegration, $skipCustomerRegistration);
                break;
        }

        $this->info('🎉 RAAST End-to-End Testing Complete');
        return 0;
    }

    /**
     * Test RTP-only flow (Pay Now) without customer registration
     */
    private function testRtpOnlyFlow($count = 1, $verbose = false, $useRealIntegration = false)
    {
        $this->info("\n💰 Testing RTP-Only Flow (Pay Now) - No Customer Registration ({$count} transaction(s))");
        $this->info("🎯 Focus: JWT Authentication & RTP Processing");
        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE (bypassing real RAAST API integration)");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION with JWT Authentication");
            $this->info("   • Real RAAST API calls to: " . config('services.raast.endpoint'));
            $this->info("   • SSL verification: " . (config('services.raast.verify_ssl') ? 'ENABLED' : 'DISABLED'));
            $this->info("   • Timeout: " . config('services.raast.timeout', 30) . " seconds");
        }
        $this->info('-' . str_repeat('-', 60));

        for ($i = 1; $i <= $count; $i++) {
            $this->info("\n🔄 RTP-Only Test #{$i}");

            // Step 1: Prepare RTP test data (no customer data needed)
            $testData = $this->generateRtpOnlyTestData();
            if ($verbose) {
                $this->info("📝 RTP Data: " . json_encode($testData, JSON_PRETTY_PRINT));
            }

            // Step 2: Test JWT token generation and validation
            $this->testJwtForRtpRequest($verbose);

            // Step 3: Test RTP processing without customer registration
            try {
                if ($useRealIntegration) {
                    // Use real RAAST API but skip customer registration
                    $result = $this->processRtpWithoutCustomerRegistration($testData, $verbose);
                } else {
                    // Create a mock successful result for RTP-only flow
                    $result = [
                        'success' => true,
                        'message' => 'RTP processed successfully (mock mode - no customer registration)',
                        'data' => [
                            'rtp_created' => true,
                            'customer_registration_skipped' => true,
                            'document_signed' => true,
                            'raast_response' => 'Mock RAAST RTP response for Pay Now testing',
                            'processing_time' => '1.2 seconds',
                            'rtp_id' => 'RTP' . time() . rand(1000, 9999),
                            'expiry_time' => now()->setTimezone('Asia/Karachi')->addMinutes(15)->format('Y-m-d\TH:i:s.vP'),
                            'payment_url' => 'https://raast.sbp.org.pk/pay/' . time()
                        ],
                        'mock_mode' => true,
                        'jwt_authenticated' => true
                    ];
                }

                if ($verbose) {
                    $this->info("📊 RTP-Only Service Response: " . json_encode($result, JSON_PRETTY_PRINT));
                }

                // Handle different response formats with enhanced XML parsing
                $success = false;
                $statusMessage = 'Unknown';
                $errorDetails = null;

                if (is_array($result)) {
                    // Check for explicit success/error flags first
                    if (isset($result['success'])) {
                        $success = $result['success'];
                        $statusMessage = $result['success'] ? 'Success (JSON)' : 'Failed (JSON)';
                    } elseif (isset($result['error']) && $result['error']) {
                        $success = false;
                        $statusMessage = 'Error: ' . ($result['message'] ?? 'Unknown error');
                        $errorDetails = $result;
                    } elseif (isset($result['response_type']) && $result['response_type'] === 'xml') {
                        // Use XML parsing results for success determination
                        $success = $result['success'] ?? false;
                        $statusMessage = $this->formatXmlStatusMessage($result);
                        if (!$success) {
                            $errorDetails = $result;
                        }
                    } elseif (isset($result['status'])) {
                        $success = $result['status'] === 'success';
                        $statusMessage = 'Status: ' . $result['status'];
                    } else {
                        // Default to success if no explicit error indicators
                        $success = true;
                        $statusMessage = 'Success (default)';
                    }
                } else {
                    $success = true;
                    $statusMessage = 'Success (non-array response)';
                }

                // Create RTP transaction record
                $transaction = RaastTransaction::create([
                    'trace_reference' => $this->generateUuid(),
                    'service_type' => 'rtp',
                    'message_type' => $testData['type'],
                    'sender' => $testData['sender'],
                    'receiver' => $testData['receiver'],
                    'amount' => $testData['amount'],
                    'currency' => $testData['currency'],
                    'status' => $success ? 'completed' : 'failed',
                    'request_data' => $testData,
                    'response_data' => $result,
                    'ip_address' => '127.0.0.1'
                ]);

                $responseData = [
                    'success' => $success,
                    'trace_reference' => $transaction->trace_reference,
                    'transaction_id' => $transaction->id,
                    'message' => $success ? 'RTP created successfully (no customer registration)' : 'RTP creation failed',
                    'data' => array_merge($result, [
                        'rtp_id' => 'RTP' . $transaction->id,
                        'expiry_date' => now()->setTimezone('Asia/Karachi')->addMinutes(15)->format('Y-m-d\TH:i:s.vP')
                    ])
                ];

                if (isset($responseData['success']) && $responseData['success']) {
                    $this->info("✅ RTP-Only Creation: SUCCESS");
                    $this->info("   Status: " . $statusMessage);
                    $this->info("   Trace Reference: " . ($responseData['trace_reference'] ?? 'N/A'));
                    $this->info("   RTP ID: " . ($responseData['data']['rtp_id'] ?? 'N/A'));
                    $this->info("   Customer Registration: SKIPPED");
                    $this->info("   JWT Authentication: " . (isset($result['jwt_authenticated']) ? 'VERIFIED' : 'N/A'));

                    // Display XML-specific information if available
                    if (isset($result['raast_message_type'])) {
                        $this->info("   RAAST Message Type: " . $result['raast_message_type']);
                    }

                    if (isset($result['transaction_status'])) {
                        $this->info("   Transaction Status: " . $result['transaction_status']);
                    }

                    // Step 4: Test RTP status
                    if (isset($responseData['trace_reference'])) {
                        $traceRef = $responseData['trace_reference'];
                        $this->testTransactionStatus($traceRef, $verbose);

                        // Step 5: Test RTP cancellation
                        $this->testRtpCancellation($traceRef);
                    }

                    if ($verbose) {
                        $this->info("📊 Full RTP-Only Response: " . json_encode($responseData, JSON_PRETTY_PRINT));
                    }
                } else {
                    $this->error("❌ RTP-Only Creation: FAILED");
                    $this->error("   Status: " . $statusMessage);
                    $this->error("   Error: " . ($responseData['message'] ?? 'Unknown error'));

                    if ($errorDetails) {
                        if (isset($errorDetails['error_code'])) {
                            $this->error("   Error Code: " . $errorDetails['error_code']);
                        }
                        if (isset($errorDetails['error_description'])) {
                            $this->error("   Error Description: " . $errorDetails['error_description']);
                        }
                        if (isset($errorDetails['xml_parse_error'])) {
                            $this->error("   XML Parse Error: " . $errorDetails['xml_parse_error']);
                        }
                        if (isset($errorDetails['raast_message_type'])) {
                            $this->error("   RAAST Message Type: " . $errorDetails['raast_message_type']);
                        }
                    }

                    // Always show raw response for debugging when there's an error
                    if (isset($errorDetails['raw_response']) && !empty($errorDetails['raw_response'])) {
                        $this->error("📄 Raw RAAST Response:");
                        $this->error("   Content-Type: " . ($errorDetails['content_type'] ?? 'Unknown'));
                        $this->error("   Response Length: " . ($errorDetails['response_length'] ?? 0) . " bytes");
                        $this->error("   Status Code: " . ($errorDetails['status_code'] ?? 'Unknown'));
                        $this->line(""); // Empty line for readability
                        $this->error("--- RAW RESPONSE START ---");
                        $this->error($errorDetails['raw_response']);
                        $this->error("--- RAW RESPONSE END ---");
                        $this->line(""); // Empty line for readability
                    } elseif (isset($responseData['data']['raast_response']['raw_response'])) {
                        $raastResp = $responseData['data']['raast_response'];
                        $this->error("📄 Raw RAAST Response (from nested data):");
                        $this->error("   Content-Type: " . ($raastResp['content_type'] ?? 'Unknown'));
                        $this->error("   Response Length: " . ($raastResp['response_length'] ?? 0) . " bytes");
                        $this->error("   Status Code: " . ($raastResp['status_code'] ?? 'Unknown'));
                        $this->error("   JSON Error: " . ($raastResp['json_error'] ?? 'Unknown'));
                        $this->line(""); // Empty line for readability
                        $this->error("--- RAW RESPONSE START ---");
                        $this->error($raastResp['raw_response']);
                        $this->error("--- RAW RESPONSE END ---");
                        $this->line(""); // Empty line for readability
                    }

                    if ($verbose) {
                        $this->error("📊 Full Error Response: " . json_encode($responseData, JSON_PRETTY_PRINT));
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ RTP-Only Processing: EXCEPTION - " . $e->getMessage());
                if ($verbose) {
                    $this->error("📊 Stack Trace: " . $e->getTraceAsString());
                }
            }
        }
    }

    /**
     * Test JWT token for RTP request
     */
    private function testJwtForRtpRequest($verbose = false)
    {
        $this->info("\n🔐 Testing JWT Authentication for RTP");

        try {
            $casService = app(\App\Services\CasApiService::class);

            // Get headers which should include JWT token
            $reflection = new \ReflectionClass($casService);
            $headersMethod = $reflection->getMethod('headers');
            $headersMethod->setAccessible(true);
            $headers = $headersMethod->invoke($casService);

            if (isset($headers['Authorization'])) {
                $authHeader = $headers['Authorization'];
                if (strpos($authHeader, 'Bearer ') === 0) {
                    $this->info("✅ JWT Authorization Header: PRESENT");
                    $token = substr($authHeader, 7); // Remove 'Bearer ' prefix
                    $this->info("   Token Length: " . strlen($token) . " characters");

                    if ($verbose) {
                        $this->info("   Full Authorization Header: " . $authHeader);
                        $this->info("   Token Preview: " . substr($token, 0, 50) . "...");

                        // Show other headers for debugging
                        $this->info("📋 All Request Headers:");
                        foreach ($headers as $key => $value) {
                            if ($key === 'Authorization') {
                                $this->info("   {$key}: Bearer [TOKEN_HIDDEN]");
                            } else {
                                $this->info("   {$key}: {$value}");
                            }
                        }
                    }
                } else {
                    $this->error("❌ JWT Authorization Header: INVALID FORMAT");
                }
            } else {
                $this->error("❌ JWT Authorization Header: MISSING");
            }

            // Test JWT token caching
            $cacheKey = 'cas_jwt_token_' . config('cas.member_id');
            $cachedToken = \Illuminate\Support\Facades\Cache::get($cacheKey);

            if ($cachedToken) {
                $this->info("✅ JWT Token Caching: ACTIVE");
                $this->info("   Cache Key: " . $cacheKey);
                $this->info("   Cache TTL: " . config('cas.jwt_cache_minutes', 4) . " minutes");
            } else {
                $this->warn("⚠️ JWT Token Caching: NOT FOUND");
            }
        } catch (\Exception $e) {
            $this->error("❌ JWT Authentication Test: ERROR - " . $e->getMessage());
        }
    }

    /**
     * Process RTP without customer registration using real RAAST integration
     */
    private function processRtpWithoutCustomerRegistration($testData, $verbose = false)
    {
        $this->info("🔄 Processing RTP without customer registration using real RAAST API...");

        try {
            // Use real RAAST API call similar to RaastController
            $traceReference = (string) \Illuminate\Support\Str::uuid();
            $traceReference = str_replace("-", "", $traceReference);

            $requestData = [
                'traceReference' => $traceReference,
                'service' => 'rtp',
                'type' => $testData['type'],
                'sender' => $testData['sender'],
                'receiver' => $testData['receiver'],
                'document' => $testData['document']
            ];

            if ($verbose) {
                $this->info("📤 RAAST API Request Data:");
                $this->info("   Trace Reference: " . $traceReference);
                $this->info("   Service: " . $requestData['service']);
                $this->info("   Type: " . $requestData['type']);
                $this->info("   Sender: " . $requestData['sender']);
                $this->info("   Receiver: " . $requestData['receiver']);
                $this->info("   Document Length: " . strlen($requestData['document']) . " characters");
            }

            // Make actual RAAST API call
            $raastResponse = $this->callRaastApi($requestData, $verbose);

            if (isset($raastResponse['error']) && $raastResponse['error']) {
                return [
                    'success' => false,
                    'error' => true,
                    'message' => 'RAAST API call failed: ' . ($raastResponse['message'] ?? 'Unknown error'),
                    'status' => $raastResponse['status'] ?? 500,
                    'raast_response' => $raastResponse
                ];
            }

            return [
                'success' => true,
                'message' => 'RTP processed successfully via real RAAST API',
                'data' => [
                    'customer_registration_skipped' => true,
                    'rtp_creation_successful' => true,
                    'jwt_token_used' => true,
                    'trace_reference' => $traceReference,
                    'raast_response' => $raastResponse
                ],
                'raast_response' => $raastResponse
            ];
        } catch (\Exception $e) {
            return [
                'success' => false,
                'error' => true,
                'message' => 'RTP processing failed: ' . $e->getMessage(),
                'status' => 500
            ];
        }
    }

    /**
     * Generate RTP-only test data (no customer information)
     */
    private function generateRtpOnlyTestData()
    {
        $messageId = 'RTPONLY' . time() . rand(100, 999);

        return [
            'service' => 'rtp',
            'type' => 'pain.013.001.09',
            'sender' => 'NITBPKKASRTP',
            'receiver' => 'BPUNPKKAXXXX',
            'document' => $this->generateTestRtpXml($messageId),
            'amount' => rand(100, 5000) / 100,
            'currency' => 'PKR',
            'rtp_id' => $messageId,
            'expiry_minutes' => 30,
            'payment_purpose' => 'Online Merchant Payment',
            'skip_customer_registration' => true,
            'business_service' => 'RTP', // Request to Pay
            'priority' => '0001',
            'customer_cnic' => '************' // Test CNIC from production XML
        ];
    }

    /**
     * Test system prerequisites
     */
    private function testPrerequisites($testJwt = false)
    {
        $this->info("\n📋 Testing System Prerequisites");

        // Test database connection
        try {
            $transactionCount = RaastTransaction::count();
            $this->info("✅ Database Connection: OK (Transactions: {$transactionCount})");
        } catch (\Exception $e) {
            $this->error("❌ Database Connection: FAILED - " . $e->getMessage());
            return;
        }

        // Test Redis connection
        try {
            \Illuminate\Support\Facades\Cache::put('raast_test', 'ok', 60);
            $value = \Illuminate\Support\Facades\Cache::get('raast_test');
            if ($value === 'ok') {
                $this->info("✅ Redis Cache: OK");
            } else {
                $this->warn("⚠️ Redis Cache: Not working properly");
            }
        } catch (\Exception $e) {
            $this->warn("⚠️ Redis Cache: " . $e->getMessage());
        }

        // Test services
        try {
            app(RaastIntegrationService::class);
            $this->info("✅ RaastIntegrationService: OK");
        } catch (\Exception $e) {
            $this->error("❌ RaastIntegrationService: FAILED - " . $e->getMessage());
        }

        // Test CAS API SSL configuration
        try {
            $casService = app(\App\Services\CasApiService::class);
            $sslCheck = $casService->checkSslConfiguration();

            if ($sslCheck['ssl_ready']) {
                $this->info("✅ CAS API SSL Configuration: OK");
            } else {
                $this->warn("⚠️ CAS API SSL Configuration: Issues detected");
                foreach ($sslCheck['recommendations'] as $recommendation) {
                    $this->warn("   • " . $recommendation);
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ CAS API SSL Check: FAILED - " . $e->getMessage());
        }

        // Test certificate files
        $privateKeyPath = storage_path('certs/private.key');
        $certificatePath = storage_path('certs/certificate.crt');

        if (file_exists($privateKeyPath) && is_readable($privateKeyPath)) {
            $this->info("✅ SSL Private Key: Available");
        } else {
            $this->warn("⚠️ SSL Private Key: Missing or not readable");
        }

        if (file_exists($certificatePath) && is_readable($certificatePath)) {
            $this->info("✅ SSL Certificate: Available");
        } else {
            $this->warn("⚠️ SSL Certificate: Missing or not readable");
        }

        // Test RAAST API configuration
        $raastEndpoint = config('services.raast.endpoint');
        $raastToken = config('services.raast.bearer_token');

        if ($raastEndpoint) {
            $this->info("✅ RAAST API Endpoint: " . $raastEndpoint);
        } else {
            $this->warn("⚠️ RAAST API Endpoint: Not configured");
        }

        if ($raastToken) {
            $this->info("✅ RAAST Bearer Token: Configured");
        } else {
            $this->warn("⚠️ RAAST Bearer Token: Not configured");
        }

        $this->info("ℹ️  Use --real-integration flag to test with actual RAAST API calls");

        // Test JWT token generation if requested
        if ($testJwt) {
            $this->testJwtTokenGeneration();
        }
    }

    /**
     * Test JWT token generation and validation
     */
    private function testJwtTokenGeneration()
    {
        $this->info("\n🔐 Testing JWT Token Generation");

        try {
            $casService = app(\App\Services\CasApiService::class);

            // Test JWT token generation
            $reflection = new \ReflectionClass($casService);
            $method = $reflection->getMethod('generateJwtToken');
            $method->setAccessible(true);

            $token = $method->invoke($casService);

            if ($token) {
                $this->info("✅ JWT Token Generation: SUCCESS");
                $this->info("   Token Length: " . strlen($token) . " characters");
                $this->info("   Token Preview: " . substr($token, 0, 50) . "...");

                // Test token caching
                $cacheKey = 'cas_jwt_token_' . config('cas.member_id');
                $cachedToken = \Illuminate\Support\Facades\Cache::get($cacheKey);

                if ($cachedToken) {
                    $this->info("✅ JWT Token Caching: SUCCESS");
                    $this->info("   Cache Key: " . $cacheKey);
                } else {
                    $this->warn("⚠️ JWT Token Caching: Not found in cache");
                }

                // Test token headers
                $headers = $reflection->getMethod('headers');
                $headers->setAccessible(true);
                $headerArray = $headers->invoke($casService);

                if (isset($headerArray['Authorization']) && strpos($headerArray['Authorization'], 'Bearer ') === 0) {
                    $this->info("✅ Authorization Header: SUCCESS");
                    $this->info("   Header: " . substr($headerArray['Authorization'], 0, 50) . "...");
                } else {
                    $this->error("❌ Authorization Header: MISSING or INVALID");
                }
            } else {
                $this->error("❌ JWT Token Generation: FAILED");
            }
        } catch (\Exception $e) {
            $this->error("❌ JWT Token Test: ERROR - " . $e->getMessage());
        }
    }

    /**
     * Test payment flow
     */
    private function testPaymentFlow($count = 1, $verbose = false, $useRealIntegration = false, $skipCustomerRegistration = false)
    {
        $this->info("\n💳 Testing Payment Flow ({$count} transaction(s))");
        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE (bypassing real RAAST API integration)");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION");
            $this->info("   • Customer registration via CAS API");
            $this->info("   • Document signing via MxSignature");
            $this->info("   • Payment processing via RAAST API");
        }
        $this->info('-' . str_repeat('-', 40));

        for ($i = 1; $i <= $count; $i++) {
            $this->info("\n🔄 Payment Test #{$i}");

            // Step 1: Prepare test data
            $testData = $this->generatePaymentTestData();
            if ($verbose) {
                $this->info("📝 Test Data: " . json_encode($testData, JSON_PRETTY_PRINT));
            }

            // Step 3: Test payment processing
            try {
                if ($useRealIntegration) {
                    // Use real RaastIntegrationService for customer registration and document signing
                    $integrationService = app(RaastIntegrationService::class);
                    $integrationResult = $integrationService->processRaastPayment([
                        'document' => $testData['document'],
                        'customer_cnic' => $testData['customer_cnic'],
                        'customer_contact' => $testData['customer_contact'],
                        'customer_name' => $testData['customer_name'],
                        'customer_address' => $testData['customer_address'] ?? null,
                    ]);

                    if (isset($integrationResult['error'])) {
                        $result = $integrationResult;
                    } else {
                        // Now make real RAAST API call with signed document
                        $traceReference = (string) \Illuminate\Support\Str::uuid();
                        $traceReference = str_replace("-", "", $traceReference);

                        $requestData = [
                            'traceReference' => $traceReference,
                            'service' => $testData['service'],
                            'type' => $testData['type'],
                            'sender' => $testData['sender'],
                            'receiver' => $testData['receiver'],
                            'document' => $integrationResult['data']['document'] // Use signed document
                        ];

                        $raastResponse = $this->callRaastApi($requestData, $verbose);

                        if (isset($raastResponse['error']) && $raastResponse['error']) {
                            $result = [
                                'success' => false,
                                'error' => true,
                                'message' => 'RAAST API call failed: ' . ($raastResponse['message'] ?? 'Unknown error'),
                                'status' => $raastResponse['status'] ?? 500,
                                'integration_result' => $integrationResult,
                                'raast_response' => $raastResponse
                            ];
                        } else {
                            $result = [
                                'success' => true,
                                'message' => 'Payment processed successfully via real RAAST API',
                                'data' => array_merge($integrationResult['data'], [
                                    'trace_reference' => $traceReference,
                                    'raast_response' => $raastResponse
                                ]),
                                'integration_result' => $integrationResult,
                                'raast_response' => $raastResponse
                            ];
                        }
                    }
                } else {
                    // Create a mock successful result to test the flow without real integration
                    $result = [
                        'success' => true,
                        'message' => 'Test payment processed successfully (mock mode)',
                        'data' => [
                            'customer_registered' => true,
                            'document_signed' => true,
                            'raast_response' => 'Mock RAAST response for testing',
                            'processing_time' => '2.5 seconds'
                        ],
                        'mock_mode' => true
                    ];
                }

                if ($verbose) {
                    $this->info("📊 Service Response: " . json_encode($result, JSON_PRETTY_PRINT));
                }

                // Handle different response formats - be defensive
                $success = false;
                if (is_array($result)) {
                    if (isset($result['success'])) {
                        $success = $result['success'];
                    } elseif (isset($result['status'])) {
                        $success = $result['status'] === 'success';
                    } elseif (!isset($result['error'])) {
                        $success = true; // No error means success
                    }
                } else {
                    // If result is not an array, assume success if no exception was thrown
                    $success = true;
                }

                // Create transaction record for testing
                $transaction = RaastTransaction::create([
                    'trace_reference' => (string) \Illuminate\Support\Str::uuid(),
                    'service_type' => $testData['service'] === 'N' ? 'payment' : 'rtp',
                    'message_type' => $testData['type'],
                    'sender' => $testData['sender'],
                    'receiver' => $testData['receiver'],
                    'amount' => $testData['amount'],
                    'currency' => $testData['currency'],
                    'status' => $success ? 'completed' : 'failed',
                    'request_data' => $testData,
                    'response_data' => $result,
                    'ip_address' => '127.0.0.1'
                ]);

                $responseData = [
                    'success' => $success,
                    'trace_reference' => $transaction->trace_reference,
                    'transaction_id' => $transaction->id,
                    'message' => $success ? 'Payment processed successfully' : 'Payment failed',
                    'data' => $result
                ];

                if (isset($responseData['success']) && $responseData['success']) {
                    $this->info("✅ Payment Processing: SUCCESS");
                    $this->info("   Trace Reference: " . ($responseData['trace_reference'] ?? 'N/A'));
                    $this->info("   Transaction ID: " . ($responseData['transaction_id'] ?? 'N/A'));

                    // Step 4: Test status checking
                    if (isset($responseData['trace_reference'])) {
                        $this->testTransactionStatus($responseData['trace_reference'], $verbose);
                    }

                    if ($verbose) {
                        $this->info("📊 Full Response: " . json_encode($responseData, JSON_PRETTY_PRINT));
                    }
                } else {
                    $this->error("❌ Payment Processing: FAILED");
                    $this->error("   Error: " . ($responseData['message'] ?? 'Unknown error'));
                    if ($verbose) {
                        $this->error("📊 Error Response: " . json_encode($responseData, JSON_PRETTY_PRINT));
                    }
                }
            } catch (\Exception $e) {
                $this->error("❌ Payment Processing: EXCEPTION - " . $e->getMessage());
                if ($verbose) {
                    $this->error("📊 Stack Trace: " . $e->getTraceAsString());
                }
            }
        }
    }

    /**
     * Test RTP flow
     */
    private function testRtpFlow($count = 1, $verbose = false, $useRealIntegration = false, $skipCustomerRegistration = false)
    {
        $this->info("\n📨 Testing RTP Flow ({$count} transaction(s))");
        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE (bypassing real RAAST API integration)");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION");
            $this->info("   • Customer registration via CAS API");
            $this->info("   • Document signing via MxSignature");
            $this->info("   • RTP processing via RAAST API");
        }
        $this->info('-' . str_repeat('-', 40));

        for ($i = 1; $i <= $count; $i++) {
            $this->info("\n🔄 RTP Test #{$i}");

            // Step 1: Prepare RTP test data
            $testData = $this->generateRtpTestData();
            if ($verbose) {
                $this->info("📝 RTP Data: " . json_encode($testData, JSON_PRETTY_PRINT));
            }

            // Step 3: Test RTP processing
            try {
                if ($useRealIntegration) {
                    // Use real RaastIntegrationService for customer registration and document signing
                    $integrationService = app(RaastIntegrationService::class);
                    $integrationResult = $integrationService->processRaastPayment([
                        'document' => $testData['document'],
                        'customer_cnic' => $testData['customer_cnic'],
                        'customer_contact' => $testData['customer_contact'],
                        'customer_name' => $testData['customer_name'],
                    ]);

                    if (isset($integrationResult['error'])) {
                        $result = $integrationResult;
                    } else {
                        // Now make real RAAST API call with signed document
                        $traceReference = (string) \Illuminate\Support\Str::uuid();
                        $traceReference = str_replace("-", "", $traceReference);

                        $requestData = [
                            'traceReference' => $traceReference,
                            'service' => 'rtp',
                            'type' => $testData['type'],
                            'sender' => $testData['sender'],
                            'receiver' => $testData['receiver'],
                            'document' => $integrationResult['data']['document'] // Use signed document
                        ];

                        $raastResponse = $this->callRaastApi($requestData, $verbose);

                        if (isset($raastResponse['error']) && $raastResponse['error']) {
                            $result = [
                                'success' => false,
                                'error' => true,
                                'message' => 'RAAST API call failed: ' . ($raastResponse['message'] ?? 'Unknown error'),
                                'status' => $raastResponse['status'] ?? 500,
                                'integration_result' => $integrationResult,
                                'raast_response' => $raastResponse
                            ];
                        } else {
                            $result = [
                                'success' => true,
                                'message' => 'RTP processed successfully via real RAAST API',
                                'data' => array_merge($integrationResult['data'], [
                                    'trace_reference' => $traceReference,
                                    'rtp_id' => 'RTP' . time() . rand(1000, 9999),
                                    'raast_response' => $raastResponse
                                ]),
                                'integration_result' => $integrationResult,
                                'raast_response' => $raastResponse
                            ];
                        }
                    }
                } else {
                    // Create a mock successful result to test the flow without real integration
                    $result = [
                        'success' => true,
                        'message' => 'Test RTP processed successfully (mock mode)',
                        'data' => [
                            'customer_registered' => true,
                            'document_signed' => true,
                            'raast_response' => 'Mock RAAST RTP response for testing',
                            'processing_time' => '1.8 seconds',
                            'rtp_id' => 'RTP' . time() . rand(1000, 9999)
                        ],
                        'mock_mode' => true
                    ];
                }

                if ($verbose) {
                    $this->info("📊 RTP Service Response: " . json_encode($result, JSON_PRETTY_PRINT));
                }

                // Handle different response formats - be defensive
                $success = false;
                if (is_array($result)) {
                    if (isset($result['success'])) {
                        $success = $result['success'];
                    } elseif (isset($result['status'])) {
                        $success = $result['status'] === 'success';
                    } elseif (!isset($result['error'])) {
                        $success = true; // No error means success
                    }
                } else {
                    // If result is not an array, assume success if no exception was thrown
                    $success = true;
                }

                // Create RTP transaction record
                $transaction = RaastTransaction::create([
                    'trace_reference' => (string) \Illuminate\Support\Str::uuid(),
                    'service_type' => $testData['service'] === 'N' ? 'payment' : 'rtp',
                    'message_type' => $testData['type'],
                    'sender' => $testData['sender'],
                    'receiver' => $testData['receiver'],
                    'amount' => $testData['amount'],
                    'currency' => $testData['currency'],
                    'status' => $success ? 'completed' : 'failed',
                    'request_data' => $testData,
                    'response_data' => $result,
                    'ip_address' => '127.0.0.1'
                ]);

                $responseData = [
                    'success' => $success,
                    'trace_reference' => $transaction->trace_reference,
                    'transaction_id' => $transaction->id,
                    'message' => $success ? 'RTP created successfully' : 'RTP creation failed',
                    'data' => array_merge($result, [
                        'rtp_id' => 'RTP' . $transaction->id,
                        'expiry_date' => now()->setTimezone('Asia/Karachi')->addDay()->format('Y-m-d\TH:i:s.vP')
                    ])
                ];

                if (isset($responseData['success']) && $responseData['success']) {
                    $this->info("✅ RTP Creation: SUCCESS");
                    $this->info("   Trace Reference: " . ($responseData['trace_reference'] ?? 'N/A'));
                    $this->info("   RTP ID: " . ($responseData['data']['rtp_id'] ?? 'N/A'));

                    // Step 4: Test RTP status
                    if (isset($responseData['trace_reference'])) {
                        $traceRef = $responseData['trace_reference'];
                        $this->testTransactionStatus($traceRef, $verbose);

                        // Step 5: Test RTP cancellation
                        $this->testRtpCancellation($traceRef);
                    }
                } else {
                    $this->error("❌ RTP Creation: FAILED");
                    $this->error("   Error: " . ($responseData['message'] ?? 'Unknown error'));
                }
            } catch (\Exception $e) {
                $this->error("❌ RTP Processing: EXCEPTION - " . $e->getMessage());
            }
        }
    }

    /**
     * Test transaction status
     */
    private function testTransactionStatus($traceReference, $verbose = false)
    {
        $this->info("\n🔍 Testing Transaction Status");

        try {
            $transaction = RaastTransaction::where('trace_reference', $traceReference)->first();

            if ($transaction) {
                $this->info("✅ Transaction Found: {$transaction->status}");
                $this->info("   Service Type: {$transaction->service_type}");
                $this->info("   Message Type: {$transaction->message_type}");
                $this->info("   Amount: {$transaction->amount} {$transaction->currency}");
                $this->info("   Created: {$transaction->created_at}");

                if ($verbose) {
                    $logs = $transaction->logs()->orderBy('created_at', 'desc')->get();
                    $this->info("📋 Transaction Logs:");
                    foreach ($logs as $log) {
                        $this->info("   [{$log->created_at}] {$log->level}: {$log->message}");
                    }
                }
            } else {
                $this->warn("⚠️ Transaction not found in database");
            }
        } catch (\Exception $e) {
            $this->error("❌ Status Check: ERROR - " . $e->getMessage());
        }
    }

    /**
     * Test RTP cancellation
     */
    private function testRtpCancellation($traceReference)
    {
        $this->info("\n❌ Testing RTP Cancellation");

        try {
            // Find the transaction and mark it as cancelled
            $transaction = RaastTransaction::where('trace_reference', $traceReference)->first();

            if ($transaction) {
                $transaction->update(['status' => 'cancelled']);
                $this->info("✅ RTP Cancellation: SUCCESS");
                $this->info("   Transaction marked as cancelled");
            } else {
                $this->warn("⚠️ RTP Cancellation: Transaction not found");
            }
        } catch (\Exception $e) {
            $this->error("❌ RTP Cancellation: ERROR - " . $e->getMessage());
        }
    }

    /**
     * Generate payment test data
     */
    private function generatePaymentTestData()
    {
        $messageId = 'TEST' . time() . rand(100, 999);

        return [
            'service' => 'N',
            'type' => 'pacs.008.001.08',
            'sender' => 'NITBPKKASRTP',
            'receiver' => 'BPUNPKKAXXXX',
            'document' => $this->generateTestXml($messageId),
            'amount' => rand(100, 10000) / 100, // Random amount between 1.00 and 100.00
            'currency' => 'PKR',
            'customer_cnic' => '12345' . rand(10000000, 99999999),
            'customer_contact' => '0300' . rand(1000000, 9999999),
            'customer_name' => 'Test Customer ' . rand(1, 1000),
            'customer_address' => 'Test Address ' . rand(1, 100) . ', Islamabad'
        ];
    }

    /**
     * Generate RTP test data
     */
    private function generateRtpTestData()
    {
        $messageId = 'RTP' . time() . rand(100, 999);
        $includeSignature = $this->option('include-signature') !== false;

        return [
            'service' => 'rtp',
            'type' => 'pain.013.001.09',
            'sender' => 'NITBPKKASRTP',
            'receiver' => 'BPUNPKKAXXXX',
            'document' => $this->generateTestRtpXml($messageId, $includeSignature),
            'amount' => rand(100, 5000) / 100,
            'currency' => 'PKR',
            'customer_cnic' => '************',
            'customer_contact' => '0300' . rand(1000000, 9999999),
            'customer_name' => 'Test RTP Customer ' . rand(1, 1000),
            'business_service' => 'RTP',
            'priority' => '0001'
        ];
    }

    /**
     * Generate test XML document
     */
    private function generateTestXml($messageId)
    {
        $instructionId = 'INSTR' . time();
        $endToEndId = 'E2E' . time();
        $amount = rand(100, 10000) / 100;

        return <<<XML
<Document xmlns="urn:iso:std:iso:20022:tech:xsd:pacs.008.001.08">
    <FIToFICstmrCdtTrf>
        <GrpHdr>
            <MsgId>{$messageId}</MsgId>
            <CreDtTm>2024-01-15T10:30:00</CreDtTm>
            <NbOfTxs>1</NbOfTxs>
            <CtrlSum>{$amount}</CtrlSum>
            <InstgAgt><FinInstnId><BIC>TESTBANK001</BIC></FinInstnId></InstgAgt>
            <InstdAgt><FinInstnId><BIC>TESTBANK002</BIC></FinInstnId></InstdAgt>
        </GrpHdr>
        <CdtTrfTxInf>
            <PmtId>
                <InstrId>{$instructionId}</InstrId>
                <EndToEndId>{$endToEndId}</EndToEndId>
            </PmtId>
            <IntrBkSttlmAmt Ccy="PKR">{$amount}</IntrBkSttlmAmt>
            <InstdAmt Ccy="PKR">{$amount}</InstdAmt>
            <Dbtr><Nm>Test Customer</Nm></Dbtr>
            <DbtrAcct><Id><IBAN>************************</IBAN></Id></DbtrAcct>
            <DbtrAgt><FinInstnId><BIC>TESTBANK001</BIC></FinInstnId></DbtrAgt>
            <Cdtr><Nm>Test Receiver</Nm></Cdtr>
            <CdtrAcct><Id><IBAN>************************</IBAN></Id></CdtrAcct>
            <CdtrAgt><FinInstnId><BIC>TESTBANK002</BIC></FinInstnId></CdtrAgt>
            <RmtInf><Ustrd>Test payment - E2E testing</Ustrd></RmtInf>
        </CdtTrfTxInf>
    </FIToFICstmrCdtTrf>
</Document>
XML;
    }

    /**
     * Generate production-ready RTP XML based on actual RAAST format with digital signature
     */
    private function generateTestRtpXml($messageId, $includeSignature = true)
    {
        $amount = rand(100, 5000) / 100;
        $uuid = $this->generateUuid();
        $bizMsgId = "NITB" . $uuid;
        $pmtInfId = $uuid;
        $instrId = $uuid;
        $endToEndId = $this->generateUuid();
        $uetr = $this->generateUuidv4(); // UETR requires proper UUIDv4 format with dashes
        // Use UTC timezone with Z suffix as required by RAAST XML schema
        $currentDateTime = now()->utc()->format('Y-m-d\TH:i:s.v\Z');
        $expiryDateTime = now()->utc()->addMinutes(30)->format('Y-m-d\TH:i:s.v\Z');
        $customerCnic = '************'; // Test CNIC
        $uri = generatePatternedToken();
        
        $xmlContent = <<<XML
            <DataPDU
                xmlns="urn:cma:stp:xsd:stp.1.0">
                <Body>
                    <AppHdr
                        xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.01">
                        <Fr>
                            <FIId>
                                <FinInstnId>
                                    <ClrSysMmbId>
                                        <MmbId>NITBPKKA</MmbId>
                                    </ClrSysMmbId>
                                </FinInstnId>
                            </FIId>
                        </Fr>
                        <To>
                            <FIId>
                                <FinInstnId>
                                    <ClrSysMmbId>
                                        <MmbId>SBPPPKKAXIPS</MmbId>
                                    </ClrSysMmbId>
                                </FinInstnId>
                            </FIId>
                        </To>
                        <BizMsgIdr>{$messageId}</BizMsgIdr>
                        <MsgDefIdr>pain.013.001.09</MsgDefIdr>
                        <BizSvc>RTP</BizSvc>
                        <CreDt>{$currentDateTime}</CreDt>
                        <Sgntr>
                            <ds:Signature
                                xmlns:ds="http://www.w3.org/2000/09/xmldsig#">
                                <ds:SignedInfo>
                                    <ds:CanonicalizationMethod Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
                                    <ds:SignatureMethod Algorithm="http://www.w3.org/2000/09/xmldsig#rsa-sha1"/>
                                    <ds:Reference URI="$uri">
                                        <ds:Transforms>
                                            <ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
                                        </ds:Transforms>
                                        <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                                        <ds:DigestValue>pFqtHIPdLvU12U1NKFHt50Jww6tKymz+PSXojdvQD1s=</ds:DigestValue>
                                    </ds:Reference>
                                    <ds:Reference Type="http://uri.etsi.org/01903/v1.3.2#SignedProperties" URI="#_f4ef4742a67b40b29b30525eaac6e2c3">
                                        <ds:Transforms>
                                            <ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
                                        </ds:Transforms>
                                        <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                                        <ds:DigestValue>rNkbQ9jKR1GFyTQS0F32oGqUwkhAugx96JSwLKNQ7uc=</ds:DigestValue>
                                    </ds:Reference>
                                    <ds:Reference>
                                        <ds:Transforms>
                                            <ds:Transform Algorithm="http://www.w3.org/2001/10/xml-exc-c14n#"/>
                                        </ds:Transforms>
                                        <ds:DigestMethod Algorithm="http://www.w3.org/2001/04/xmlenc#sha256"/>
                                        <ds:DigestValue>GfkX/KZ848GbkdvNzcQJSMttUmq24sKISw8mGcHzwrg=</ds:DigestValue>
                                    </ds:Reference>
                                </ds:SignedInfo>
                                <ds:SignatureValue>JfziKNViAhw2BaF4XOuOUyJ4JT7gEwvNGFg8XFu6L3ItEvsutLWeSFSmMP14qQrpO2rxZASzPZxi OrqyYy5ZzJkuKHvos6ttGNYGefxGHwwoALaOTLt2flmxAzgU6xKY5xMIBRkrMIPb0JxvQ1CZWd2k BehUy+19EQhDklZ6NsE5EMjpm4RnqbO9qo94OoNnPCuf+JZeeUL3NwFdWfEQlcRln3k1/ihR5LJ/ P34xVdbqbHHoO9s6dP49Drt14WUajVZxCD4Da6tHdE3aNOoARMAh/ZuD+/Y63eQWqUxLv6MjWGnv Sb4/Tbjw9eWETPSaLMU7VyEAUmO+gfTb5mYPzA==</ds:SignatureValue>
                                <ds:KeyInfo Id="_503485b270c6411aa7ec6465c59a7582">
                                    <ds:X509Data>
                                        <ds:X509IssuerSerial>
                                            <ds:X509IssuerName>CN=test-TST-AD-CA, DC=test, DC=mpg, DC=local</ds:X509IssuerName>
                                            <ds:X509SerialNumber>2408480492104447439531423907480384741411129349</ds:X509SerialNumber>
                                        </ds:X509IssuerSerial>
                                    </ds:X509Data>
                                </ds:KeyInfo>
                                <ds:Object>
                                    <xades:QualifyingProperties
                                        xmlns:xades="http://uri.etsi.org/01903/v1.3.2#">
                                        <xades:SignedProperties Id="_f4ef4742a67b40b29b30525eaac6e2c3">
                                            <xades:SignedSignatureProperties>
                                                <xades:SigningTime>2025-07-28T11:18:33Z</xades:SigningTime>
                                            </xades:SignedSignatureProperties>
                                        </xades:SignedProperties>
                                    </xades:QualifyingProperties>
                                </ds:Object>
                            </ds:Signature>
                        </Sgntr>
                    </AppHdr>
                    <Document
                        xmlns="urn:iso:std:iso:20022:tech:xsd:pain.013.001.09">
                        <CdtrPmtActvtnReq>
                            <GrpHdr>
                                <MsgId>2b1d33e360704d80a175d61f43ed6557</MsgId>
                                <CreDtTm>2025-07-28T11:18:33</CreDtTm>
                                <NbOfTxs>1</NbOfTxs>
                                <InitgPty>
                                    <Id>
                                        <OrgId>
                                            <AnyBIC>ONELINKG</AnyBIC>
                                        </OrgId>
                                    </Id>
                                </InitgPty>
                            </GrpHdr>
                            <PmtInf>
                                <PmtInfId>250728000001000001</PmtInfId>
                                <PmtMtd>TRF</PmtMtd>
                                <ReqdExctnDt>
                                    <DtTm>2025-07-28T11:18:33</DtTm>
                                </ReqdExctnDt>
                                <XpryDt>
                                    <DtTm>2025-07-28T11:48:51</DtTm>
                                </XpryDt>
                                <Dbtr/>
                                <DbtrAgt>
                                    <FinInstnId>
                                        <ClrSysMmbId>
                                            <MmbId>NOTPROVIDED</MmbId>
                                        </ClrSysMmbId>
                                    </FinInstnId>
                                </DbtrAgt>
                                <CdtTrfTx>
                                    <PmtId>
                                        <InstrId>6404fbdf57d04054a25d2f2b1c7e477e</InstrId>
                                        <EndToEndId>2428896534ed4e3cb748fe1ea1c1a278</EndToEndId>
                                        <UETR>bcfbb16c-ff7e-4bfb-a18c-1a065acfffef</UETR>
                                    </PmtId>
                                    <PmtTpInf>
                                        <SvcLvl>
                                            <Prtry>0100</Prtry>
                                        </SvcLvl>
                                        <LclInstrm>
                                            <Prtry>PMCT</Prtry>
                                        </LclInstrm>
                                        <CtgyPurp>
                                            <Prtry>050</Prtry>
                                        </CtgyPurp>
                                    </PmtTpInf>
                                    <PmtCond>
                                        <AmtModAllwd>false</AmtModAllwd>
                                        <EarlyPmtAllwd>false</EarlyPmtAllwd>
                                        <DelyPnlty>0.0</DelyPnlty>
                                        <GrntedPmtReqd>false</GrntedPmtReqd>
                                    </PmtCond>
                                    <Amt>
                                        <InstdAmt Ccy="PKR">80.0</InstdAmt>
                                    </Amt>
                                    <ChrgBr>SLEV</ChrgBr>
                                    <CdtrAgt>
                                        <FinInstnId>
                                            <ClrSysMmbId>
                                                <MmbId>ONELINKG</MmbId>
                                            </ClrSysMmbId>
                                        </FinInstnId>
                                    </CdtrAgt>
                                    <Cdtr>
                                        <Nm>New Merchant</Nm>
                                        <PstlAdr>
                                            <SubDept>0001</SubDept>
                                            <TwnNm>Karachi</TwnNm>
                                            <AdrLine>8c zamazam</AdrLine>
                                        </PstlAdr>
                                        <Id>
                                            <OrgId>
                                                <Othr>
                                                    <Id>742</Id>
                                                    <SchmeNm>
                                                        <Prtry>MCC</Prtry>
                                                    </SchmeNm>
                                                </Othr>
                                                <Othr>
                                                    <Id>999930</Id>
                                                    <SchmeNm>
                                                        <Prtry>ALAS</Prtry>
                                                    </SchmeNm>
                                                </Othr>
                                            </OrgId>
                                        </Id>
                                        <CtryOfRes>PK</CtryOfRes>
                                        <CtctDtls>
                                            <Nm>New Merchant </Nm>
                                            <PhneNb>+92-3001234567</PhneNb>
                                            <MobNb>+92-3001234567</MobNb>
                                            <Othr>
                                                <ChanlTp>WEBS</ChanlTp>
                                                <Id>www.google.com</Id>
                                            </Othr>
                                            <Othr>
                                                <ChanlTp>LONG</ChanlTp>
                                                <Id>67.0739344</Id>
                                            </Othr>
                                            <Othr>
                                                <ChanlTp>LATD</ChanlTp>
                                                <Id>24.7896814</Id>
                                            </Othr>
                                        </CtctDtls>
                                    </Cdtr>
                                    <CdtrAcct>
                                        <Id>
                                            <IBAN>************************</IBAN>
                                        </Id>
                                    </CdtrAcct>
                                    <RmtInf>
                                        <Ustrd>RTP initiated by New Merchant </Ustrd>
                                        <Strd>
                                            <RfrdDocInf>
                                                <Tp>
                                                    <CdOrPrtry>
                                                        <Prtry>RDQR</Prtry>
                                                    </CdOrPrtry>
                                                </Tp>
                                            </RfrdDocInf>
                                            <RfrdDocAmt>
                                                <DuePyblAmt Ccy="PKR">0.0</DuePyblAmt>
                                                <AdjstmntAmtAndRsn>
                                                    <Amt Ccy="PKR">0.0</Amt>
                                                    <CdtDbtInd>CRDT</CdtDbtInd>
                                                    <Rsn>SCFE</Rsn>
                                                </AdjstmntAmtAndRsn>
                                            </RfrdDocAmt>
                                        </Strd>
                                    </RmtInf>
                                </CdtTrfTx>
                            </PmtInf>
                        </CdtrPmtActvtnReq>
                    </Document>
                </Body>
            </DataPDU>
        XML;

        $xmlContent = <<<XML
            <DataPDU xmlns="urn:cma:stp:xsd:stp.1.0">
                <Body>
                    <AppHdr
                        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                        xmlns="urn:iso:std:iso:20022:tech:xsd:head.001.001.01">
                        <Fr>
                            <FIId>
                                <FinInstnId>
                                    <ClrSysMmbId>
                                        <MmbId>NITBPKKA</MmbId>
                                    </ClrSysMmbId>
                                </FinInstnId>
                            </FIId>
                        </Fr>
                        <To>
                            <FIId>
                                <FinInstnId>
                                    <ClrSysMmbId>
                                        <MmbId>SBPPPKKAXIPS</MmbId>
                                    </ClrSysMmbId>
                                </FinInstnId>
                            </FIId>
                        </To>
                        <BizMsgIdr>{$bizMsgId}</BizMsgIdr>
                        <MsgDefIdr>pain.013.001.09</MsgDefIdr>
                        <BizSvc>RTP</BizSvc>
                        <CreDt>{$currentDateTime}</CreDt>
                        <Prty>0001</Prty>
                    </AppHdr>
                    <Document
                        xmlns="urn:iso:std:iso:20022:tech:xsd:pain.013.001.09">
                        <CdtrPmtActvtnReq>
                            <GrpHdr>
                                <MsgId>{$messageId}</MsgId>
                                <CreDtTm>{$currentDateTime}</CreDtTm>
                                <NbOfTxs>1</NbOfTxs>
                                <InitgPty>
                                    <Id>
                                        <OrgId>
                                            <AnyBIC>NITBPKKA</AnyBIC>
                                        </OrgId>
                                    </Id>
                                </InitgPty>
                            </GrpHdr>
                            <PmtInf>
                                <PmtInfId>{$pmtInfId}</PmtInfId>
                                <PmtMtd>TRF</PmtMtd>
                                <ReqdExctnDt>
                                    <DtTm>{$currentDateTime}</DtTm>
                                </ReqdExctnDt>
                                <XpryDt>
                                    <DtTm>{$expiryDateTime}</DtTm>
                                </XpryDt>
                                <Dbtr>
                                    <Nm>HAMMAD AZIZ KHAN</Nm>
                                </Dbtr>
                                <DbtrAcct>
                                    <Id>
                                        <IBAN>************************</IBAN>
                                    </Id>
                                </DbtrAcct>
                                <DbtrAgt>
                                    <FinInstnId>
                                        <ClrSysMmbId>
                                            <MmbId>BPUNPKKA</MmbId>
                                        </ClrSysMmbId>
                                    </FinInstnId>
                                </DbtrAgt>
                                <CdtTrfTx>
                                    <PmtId>
                                        <InstrId>{$instrId}</InstrId>
                                        <EndToEndId>{$endToEndId}</EndToEndId>
                                        <UETR>{$uetr}</UETR>
                                    </PmtId>
                                    <PmtTpInf>
                                        <SvcLvl>
                                            <Prtry>0100</Prtry>
                                        </SvcLvl>
                                        <LclInstrm>
                                            <Prtry>PMCT</Prtry>
                                        </LclInstrm>
                                        <CtgyPurp>
                                            <Prtry>050</Prtry>
                                        </CtgyPurp>
                                    </PmtTpInf>
                                    <PmtCond>
                                        <AmtModAllwd>false</AmtModAllwd>
                                        <EarlyPmtAllwd>true</EarlyPmtAllwd>
                                        <DelyPnlty>0.00</DelyPnlty>
                                        <GrntedPmtReqd>false</GrntedPmtReqd>
                                    </PmtCond>
                                    <Amt>
                                        <InstdAmt Ccy="PKR">{$amount}</InstdAmt>
                                    </Amt>
                                    <ChrgBr>SLEV</ChrgBr>
                                    <CdtrAgt>
                                        <FinInstnId>
                                            <ClrSysMmbId>
                                                <MmbId>NITBPKKA</MmbId>
                                            </ClrSysMmbId>
                                        </FinInstnId>
                                    </CdtrAgt>
                                    <Cdtr>
                                        <Nm>HAMMAD AZIZ KHAN</Nm>
                                        <PstlAdr>
                                            <TwnNm>Islamabad</TwnNm>
                                            <AdrLine>Unnamed Road, F-8 Markaz F 8 Markaz F-8, Islamabad, Islamabad Capital</AdrLine>
                                        </PstlAdr>
                                        <Id>
                                            <OrgId>
                                                <Othr>
                                                    <Id>5814</Id>
                                                    <SchmeNm>
                                                        <Prtry>MCC</Prtry>
                                                    </SchmeNm>
                                                </Othr>
                                            </OrgId>
                                        </Id>
                                        <CtryOfRes>PK</CtryOfRes>
                                        <CtctDtls>
                                            <Nm>Mohsin Cafeee</Nm>
                                            <PhneNb>+92-3218511978</PhneNb>
                                            <MobNb>+92-3218511978</MobNb>
                                            <EmailAdr><EMAIL></EmailAdr>
                                            <Dept>Mohsin Cafeee</Dept>
                                        </CtctDtls>
                                    </Cdtr>
                                    <CdtrAcct>
                                        <Id>
                                            <IBAN>************************</IBAN>
                                        </Id>
                                    </CdtrAcct>
                                    <Purp>
                                        <Prtry>Online Merchant Payment</Prtry>
                                    </Purp>
                                    <RmtInf>
                                        <Strd>
                                            <CdtrRefInf>
                                                <Tp>
                                                    <CdOrPrtry>
                                                        <Prtry>RTP</Prtry>
                                                    </CdOrPrtry>
                                                </Tp>
                                            </CdtrRefInf>
                                        </Strd>
                                    </RmtInf>
                                </CdtTrfTx>
                            </PmtInf>
                        </CdtrPmtActvtnReq>
                    </Document>
                </Body>
            </DataPDU>
        XML;

        // Validate base XML structure before signing
        $doc = new \DOMDocument();
        $doc->preserveWhiteSpace = false;
        $doc->formatOutput = true;

        if (!$doc->loadXML($xmlContent)) {
            \Log::error('Invalid base XML structure before signing');
            throw new \Exception('Invalid XML structure generated');
        }

        // Get properly formatted XML without XML declaration (RAAST API requirement)
        $xmlContent = $doc->saveXML($doc->documentElement);

        // Add digital signature if requested
        if ($includeSignature) {
            try {
                $signatureService = app(XmlDigitalSignatureService::class);
                $xmlContent = $signatureService->signXmlDocument($xmlContent, true);

                return $xmlContent;
            } catch (\Exception $e) {
                // If signature fails, log warning and return unsigned XML
                \Log::warning('Failed to add digital signature to RTP XML: ' . $e->getMessage());
                \Log::warning('Exception details: ' . $e->getTraceAsString());
                return $xmlContent;
            }
        }

        return $xmlContent;
    }

    /**
     * Generate UUID without dashes for RAAST messages (except UETR which needs dashes)
     */
    private function generateUuid()
    {
        return str_replace('-', '', \Illuminate\Support\Str::uuid()->toString());
    }

    /**
     * Generate proper UUIDv4 with dashes for UETR field
     */
    private function generateUuidv4()
    {
        return \Illuminate\Support\Str::uuid()->toString();
    }

    /**
     * Test comprehensive Pay Now RTP scenarios based on test cases
     */
    private function testComprehensivePayNowScenarios($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🧪 Comprehensive Pay Now RTP Test Scenarios");
        $this->info("=" . str_repeat('=', 60));

        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE - Some scenarios may not be fully testable");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION");
        }

        // Test Case Group A: Successful flows
        $this->info("\n📋 Group A: Successful Pay Now RTP Flows");
        $this->testSuccessfulPayNowFlow($verbose, $useRealIntegration);
        $this->testPaymentWithIncorrectDataThenCorrect($verbose, $useRealIntegration);

        // Test Case Group B: Amount modification scenarios
        $this->info("\n📋 Group B: Amount Modification Scenarios");
        $this->testAmountModificationAllowed($verbose, $useRealIntegration);
        $this->testAmountModificationDisallowed($verbose, $useRealIntegration);

        // Test Case Group C: Expiration scenarios
        $this->info("\n📋 Group C: Expiration and Timeout Scenarios");
        $this->testRtpExpiration($verbose, $useRealIntegration);
        $this->testInvalidExpirationTime($verbose, $useRealIntegration);

        // Test Case Group D: Error scenarios
        $this->info("\n📋 Group D: Error and Edge Case Scenarios");
        $this->testDuplicateRtpReferences($verbose, $useRealIntegration);
        $this->testPaymentAfterExpiration($verbose, $useRealIntegration);
        $this->testNonExistentRtpReference($verbose, $useRealIntegration);

        // Test Case Group E: Status request scenarios
        $this->info("\n📋 Group E: RTP Status Request Scenarios");
        $this->testRtpStatusRequests($verbose, $useRealIntegration);

        $this->info("\n🎉 Comprehensive Pay Now RTP Testing Complete");
    }

    /**
     * Test successful Pay Now RTP flow (Test Case A.1)
     * MSP sends correct "Pay now" RTP (pain.013) to Bank A with all contextual fields
     * Bank A sends correct payment (pacs.008) with RTP reference to MSP within RTP expiration time interval
     */
    private function testSuccessfulPayNowFlow($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test Case A.1: Successful Pay Now RTP Flow");
        $this->info("   • MSP sends correct Pay Now RTP (pain.013)");
        $this->info("   • Bank sends correct payment (pacs.008) with RTP reference");

        try {
            // Step 1: Generate test data with all contextual fields
            $testData = $this->generateRtpTestData();
            $testData['payment_purpose'] = 'Online Merchant Payment';
            $testData['business_service'] = 'RTP';
            $testData['priority'] = '0001';

            // Set AmtModAllwd to false (default)
            $testData['amount_modification_allowed'] = false;

            if ($verbose) {
                $this->info("📝 Test Data: " . json_encode($testData, JSON_PRETTY_PRINT));
            }

            // Step 2: Process RTP
            $result = $this->processTestRtp($testData, $verbose, $useRealIntegration);

            if (isset($result['success']) && $result['success']) {
                $this->info("✅ Pay Now RTP Creation: SUCCESS");
                $this->info("   Expected: MSP receives pain.014 (RTP status, ACSP)");
                $this->info("   Expected: Bank receives pain.013 (RTP)");

                // Step 3: Simulate Bank sending payment (pacs.008)
                $paymentResult = $this->simulateBankPayment($result, $testData, $verbose, $useRealIntegration);

                if (isset($paymentResult['success']) && $paymentResult['success']) {
                    $this->info("✅ Bank Payment (pacs.008): SUCCESS");
                    $this->info("   Expected: MSP receives pacs.008");
                    $this->info("   Expected: Bank receives pacs.002 (ACSP)");
                } else {
                    $this->error("❌ Bank Payment (pacs.008): FAILED");
                    if ($verbose && isset($paymentResult['message'])) {
                        $this->error("   Error: " . $paymentResult['message']);
                    }
                }
            } else {
                $this->error("❌ Pay Now RTP Creation: FAILED");
                if ($verbose && isset($result['message'])) {
                    $this->error("   Error: " . $result['message']);
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Test Case A.1: EXCEPTION - " . $e->getMessage());
            if ($verbose) {
                $this->error("   Stack Trace: " . $e->getTraceAsString());
            }
        }
    }

    /**
     * Test payment with incorrect data then correct (Test Case B)
     * MSP sends correct "Pay now" RTP (pain.013) to Bank A
     * Bank A sends payment (pacs.008) with incorrect business data, then correct payment
     */
    private function testPaymentWithIncorrectDataThenCorrect($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test Case B: Payment with Incorrect Data Then Correct");
        $this->info("   • MSP sends correct Pay Now RTP (pain.013)");
        $this->info("   • Bank sends payment with incorrect data (rejected)");
        $this->info("   • Bank sends correct payment (accepted)");

        try {
            // Step 1: Generate test data
            $testData = $this->generateRtpTestData();

            if ($verbose) {
                $this->info("📝 Test Data: " . json_encode($testData, JSON_PRETTY_PRINT));
            }

            // Step 2: Process RTP
            $result = $this->processTestRtp($testData, $verbose, $useRealIntegration);

            if (isset($result['success']) && $result['success']) {
                $this->info("✅ Pay Now RTP Creation: SUCCESS");
                $this->info("   Expected: MSP receives pain.014 (RTP status, ACSP)");
                $this->info("   Expected: Bank receives pain.013 (RTP)");

                // Step 3: Simulate Bank sending incorrect payment (pacs.008)
                $incorrectData = $testData;
                $incorrectData['amount'] = $testData['amount'] * 2; // Incorrect amount
                $incorrectData['customer_cnic'] = '*************'; // Incorrect CNIC

                $incorrectPaymentResult = $this->simulateBankPayment($result, $incorrectData, $verbose, $useRealIntegration);

                $this->info("✅ Bank Payment with Incorrect Data: SIMULATED");
                $this->info("   Expected: Bank receives pacs.002 (RJCT)");

                // Step 4: Simulate Bank sending correct payment (pacs.008)
                $correctPaymentResult = $this->simulateBankPayment($result, $testData, $verbose, $useRealIntegration);

                if (isset($correctPaymentResult['success']) && $correctPaymentResult['success']) {
                    $this->info("✅ Bank Payment with Correct Data: SUCCESS");
                    $this->info("   Expected: MSP receives pacs.008");
                    $this->info("   Expected: Bank receives pacs.002 (ACSP)");
                } else {
                    $this->error("❌ Bank Payment with Correct Data: FAILED");
                    if ($verbose && isset($correctPaymentResult['message'])) {
                        $this->error("   Error: " . $correctPaymentResult['message']);
                    }
                }
            } else {
                $this->error("❌ Pay Now RTP Creation: FAILED");
                if ($verbose && isset($result['message'])) {
                    $this->error("   Error: " . $result['message']);
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Test Case B: EXCEPTION - " . $e->getMessage());
            if ($verbose) {
                $this->error("   Stack Trace: " . $e->getTraceAsString());
            }
        }
    }

    /**
     * Test amount modification scenarios
     */
    private function testAmountModificationScenarios($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🧪 Amount Modification Test Scenarios");
        $this->info("=" . str_repeat('=', 60));

        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE - Some scenarios may not be fully testable");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION");
        }

        // Test amount modification allowed
        $this->testAmountModificationAllowed($verbose, $useRealIntegration);

        // Test amount modification disallowed
        $this->testAmountModificationDisallowed($verbose, $useRealIntegration);

        $this->info("\n🎉 Amount Modification Testing Complete");
    }

    /**
     * Test amount modification allowed (Test Case C)
     * MSP sends "Pay now" RTP with amount modification allowed
     * Bank sends payment with amount less/more than requested
     */
    private function testAmountModificationAllowed($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test Case C: Amount Modification Allowed");
        $this->info("   • MSP sends Pay Now RTP with AmountModificationAllowed=true");
        $this->info("   • Bank sends payment with different amount");

        try {
            // Step 1: Generate test data with amount modification allowed
            $testData = $this->generateRtpTestData();
            $testData['amount_modification_allowed'] = true;

            // Modify the XML to set AmtModAllwd to true
            $xml = $testData['document'];
            $xml = str_replace('<AmtModAllwd>false</AmtModAllwd>', '<AmtModAllwd>true</AmtModAllwd>', $xml);
            $testData['document'] = $xml;

            if ($verbose) {
                $this->info("📝 Test Data: " . json_encode($testData, JSON_PRETTY_PRINT));
                $this->info("   Amount Modification Allowed: true");
            }

            // Step 2: Process RTP
            $result = $this->processTestRtp($testData, $verbose, $useRealIntegration);

            if (isset($result['success']) && $result['success']) {
                $this->info("✅ Pay Now RTP Creation with Amount Modification Allowed: SUCCESS");
                $this->info("   Expected: MSP receives pain.014 (RTP status, ACSP)");
                $this->info("   Expected: Bank receives pain.013 (RTP)");

                // Step 3: Simulate Bank sending payment with less amount
                $lessAmountData = $testData;
                $lessAmountData['amount'] = $testData['amount'] * 0.8; // 80% of requested amount

                $lessAmountResult = $this->simulateBankPayment($result, $lessAmountData, $verbose, $useRealIntegration);

                if (isset($lessAmountResult['success']) && $lessAmountResult['success']) {
                    $this->info("✅ Bank Payment with Less Amount: SUCCESS");
                    $this->info("   Expected: MSP receives pacs.008");
                    $this->info("   Expected: Bank receives pacs.002 (ACSP)");
                } else {
                    $this->error("❌ Bank Payment with Less Amount: FAILED");
                    if ($verbose && isset($lessAmountResult['message'])) {
                        $this->error("   Error: " . $lessAmountResult['message']);
                    }
                }

                // Step 4: Simulate Bank sending payment with more amount
                $moreAmountData = $testData;
                $moreAmountData['amount'] = $testData['amount'] * 1.2; // 120% of requested amount

                $moreAmountResult = $this->simulateBankPayment($result, $moreAmountData, $verbose, $useRealIntegration);

                if (isset($moreAmountResult['success']) && $moreAmountResult['success']) {
                    $this->info("✅ Bank Payment with More Amount: SUCCESS");
                    $this->info("   Expected: MSP receives pacs.008");
                    $this->info("   Expected: Bank receives pacs.002 (ACSP)");
                } else {
                    $this->error("❌ Bank Payment with More Amount: FAILED");
                    if ($verbose && isset($moreAmountResult['message'])) {
                        $this->error("   Error: " . $moreAmountResult['message']);
                    }
                }
            } else {
                $this->error("❌ Pay Now RTP Creation: FAILED");
                if ($verbose && isset($result['message'])) {
                    $this->error("   Error: " . $result['message']);
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Test Case C: EXCEPTION - " . $e->getMessage());
            if ($verbose) {
                $this->error("   Stack Trace: " . $e->getTraceAsString());
            }
        }
    }

    /**
     * Test amount modification disallowed (Test Case D)
     * MSP sends "Pay now" RTP with amount modification disallowed
     * Bank sends payment with amount less/more than requested
     */
    private function testAmountModificationDisallowed($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test Case D: Amount Modification Disallowed");
        $this->info("   • MSP sends Pay Now RTP with AmountModificationAllowed=false");
        $this->info("   • Bank sends payment with different amount (should be rejected)");

        try {
            // Step 1: Generate test data with amount modification disallowed
            $testData = $this->generateRtpTestData();
            $testData['amount_modification_allowed'] = false;

            // Modify the XML to set AmtModAllwd to false
            $xml = $testData['document'];
            $xml = str_replace('<AmtModAllwd>true</AmtModAllwd>', '<AmtModAllwd>false</AmtModAllwd>', $xml);
            $testData['document'] = $xml;

            if ($verbose) {
                $this->info("📝 Test Data: " . json_encode($testData, JSON_PRETTY_PRINT));
                $this->info("   Amount Modification Allowed: false");
            }

            // Step 2: Process RTP
            $result = $this->processTestRtp($testData, $verbose, $useRealIntegration);

            if (isset($result['success']) && $result['success']) {
                $this->info("✅ Pay Now RTP Creation with Amount Modification Disallowed: SUCCESS");
                $this->info("   Expected: MSP receives pain.014 (RTP status, ACSP)");
                $this->info("   Expected: Bank receives pain.013 (RTP)");

                // Step 3: Simulate Bank sending payment with less amount (should be rejected)
                $lessAmountData = $testData;
                $lessAmountData['amount'] = $testData['amount'] * 0.8; // 80% of requested amount

                $lessAmountResult = $this->simulateBankPayment($result, $lessAmountData, $verbose, $useRealIntegration);

                $this->info("✅ Bank Payment with Less Amount: SIMULATED");
                $this->info("   Expected: MSP receives pain.014 (RTP status, RJCT)");
                $this->info("   Expected: Bank receives pacs.002 (RJCT)");

                // Step 4: Simulate Bank sending payment with more amount (should be rejected)
                $moreAmountData = $testData;
                $moreAmountData['amount'] = $testData['amount'] * 1.2; // 120% of requested amount

                $moreAmountResult = $this->simulateBankPayment($result, $moreAmountData, $verbose, $useRealIntegration);

                $this->info("✅ Bank Payment with More Amount: SIMULATED");
                $this->info("   Expected: MSP receives pain.014 (RTP status, RJCT)");
                $this->info("   Expected: Bank receives pacs.002 (RJCT)");

                // Step 5: Simulate Bank sending payment with exact amount (should be accepted)
                $exactAmountResult = $this->simulateBankPayment($result, $testData, $verbose, $useRealIntegration);

                if (isset($exactAmountResult['success']) && $exactAmountResult['success']) {
                    $this->info("✅ Bank Payment with Exact Amount: SUCCESS");
                    $this->info("   Expected: MSP receives pacs.008");
                    $this->info("   Expected: Bank receives pacs.002 (ACSP)");
                } else {
                    $this->error("❌ Bank Payment with Exact Amount: FAILED");
                    if ($verbose && isset($exactAmountResult['message'])) {
                        $this->error("   Error: " . $exactAmountResult['message']);
                    }
                }
            } else {
                $this->error("❌ Pay Now RTP Creation: FAILED");
                if ($verbose && isset($result['message'])) {
                    $this->error("   Error: " . $result['message']);
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ Test Case D: EXCEPTION - " . $e->getMessage());
            if ($verbose) {
                $this->error("   Stack Trace: " . $e->getTraceAsString());
            }
        }
    }

    /**
     * Call RAAST API with retry logic (similar to RaastController)
     */
    private function callRaastApi($requestData, $verbose = false)
    {
        $maxRetries = config('services.raast.max_retries', 3);
        $retryDelay = config('services.raast.retry_delay', 1); // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            try {
                // Configure HTTP client with SSL options
                $verifySSL = config('services.raast.verify_ssl', false); // Default to false for development
                $caPath = config('services.raast.ca_path');

                // Determine SSL verification strategy
                $sslOptions = [
                    'timeout' => config('services.raast.timeout', 30),
                    'connect_timeout' => config('services.raast.connect_timeout', 10),
                ];

                if ($verifySSL && $caPath && file_exists(storage_path($caPath))) {
                    // Use CA bundle if available
                    $sslOptions['verify'] = storage_path($caPath);
                    $sslOptions['curl'] = [
                        CURLOPT_SSL_VERIFYPEER => true,
                        CURLOPT_SSL_VERIFYHOST => 2,
                        CURLOPT_CAINFO => storage_path($caPath),
                    ];
                } elseif ($verifySSL) {
                    // Use system default CA bundle
                    $sslOptions['verify'] = true;
                    $sslOptions['curl'] = [
                        CURLOPT_SSL_VERIFYPEER => true,
                        CURLOPT_SSL_VERIFYHOST => 2,
                    ];
                } else {
                    // Disable SSL verification (for development/self-signed certificates)
                    $sslOptions['verify'] = false;
                    $sslOptions['curl'] = [
                        CURLOPT_SSL_VERIFYPEER => false,
                        CURLOPT_SSL_VERIFYHOST => 0,
                    ];
                }

                if ($verbose) {
                    $this->info("🔒 SSL Configuration:");
                    $this->info("   SSL Verification: " . ($verifySSL ? 'ENABLED' : 'DISABLED'));
                    $this->info("   CA Path: " . ($caPath ? $caPath : 'Not configured'));
                    $this->info("   Verify Option: " . ($sslOptions['verify'] === false ? 'false' : ($sslOptions['verify'] === true ? 'true' : $sslOptions['verify'])));
                }

                $client = new \GuzzleHttp\Client($sslOptions);

                $endpoint = config('services.raast.endpoint_rtp') . '/input/' . $requestData['traceReference'];

                // Try to get access token with automatic refresh
                $accessToken = $this->getRaastAccessToken();

                if (!$accessToken) {
                    // Fallback to direct bearer token if token manager fails
                    $accessToken = config('services.raast.bearer_token');

                    if (!$accessToken) {
                        // Try to generate new tokens using the token manager
                        $tokenManager = $this->getTokenManager();
                        if ($tokenManager->generateNewTokens()) {
                            $accessToken = $tokenManager->getStoredAccessToken();
                        }

                        if (!$accessToken) {
                            throw new \Exception('Failed to obtain RAAST access token. Please run: php artisan raast:oauth-token --detailed');
                        }
                    }
                }

                $headers = [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json',
                    'Accept' => 'application/json',
                    'X-Timestamp' => now()->setTimezone('Asia/Karachi')->format('Y-m-d\TH:i:s.vP'),
                    'Host' => parse_url(config('services.raast.endpoint'), PHP_URL_HOST) . ":23432",
                    'User-Agent' => 'Islamabad-City-App/1.0'
                ];

                if ($verbose) {
                    $oauth2Service = app(\App\Services\RaastOAuth2Service::class);
                    $tokenInfo = $oauth2Service->getTokenInfo($accessToken);
                    $authMethod = config('services.raast.bearer_token') ? 'Direct Bearer Token' : 'OAuth2 Flow';

                    $this->info("🔑 Authentication Details:");
                    $this->info("   Method: " . $authMethod);
                    $this->info("   Access Token: " . ($accessToken ? 'Available (' . strlen($accessToken) . ' chars)' : 'FAILED'));
                    $this->info("   Token Type: " . ($tokenInfo['asrv_type'] ?? 'Unknown'));
                    $this->info("   Expires At: " . ($tokenInfo['expires_at'] ?? 'Unknown'));
                    $this->info("   Time to Expiry: " . ($tokenInfo['time_to_expiry'] ?? 0) . ' seconds');
                    $this->info("   Authorization Header: Bearer " . substr($accessToken, 0, 10) . '...');
                }

                $body = [
                    'traceReference' => $requestData['traceReference'],
                    'service' => $requestData['service'],
                    'type' => $requestData['type'],
                    'sender' => $requestData['sender'],
                    'receiver' => $requestData['receiver'],
                    'document' => $requestData['document']
                ];

                if ($verbose) {
                    $this->info("📡 Making RAAST API Call:");
                    $this->info("   Endpoint: " . $endpoint);
                    $this->info("   Method: POST");
                    $this->info("   Attempt: {$attempt}/{$maxRetries}");
                    $this->info("   Headers: " . json_encode(array_merge($headers, ['Authorization' => 'Bearer [HIDDEN]']), JSON_PRETTY_PRINT));
                }

                // Use token refresh functionality for the API call
                $result = $this->executeRaastApiCall(function ($validAccessToken) use ($client, $endpoint, $headers, $body) {
                    // Update headers with fresh token
                    $headers['Authorization'] = 'Bearer ' . $validAccessToken;

                    $response = $client->post($endpoint, [
                        'headers' => $headers,
                        'json' => $body
                    ]);

                    $responseBody = $response->getBody()->getContents();

                    // Check for token expiration in response
                    if ($this->isRaastTokenExpired($response, $responseBody)) {
                        throw new \Exception('Token expired: ' . $responseBody);
                    }

                    $responseArray = json_decode($responseBody, true);

                    if (json_last_error() !== JSON_ERROR_NONE) {
                        // Handle non-JSON responses gracefully
                        $jsonError = json_last_error_msg();
                        $statusCode = $response->getStatusCode();
                        $contentType = $response->getHeaderLine('Content-Type');

                        // Check if this is a successful response with XML content OR empty successful response
                        $isSuccessfulXml = ($statusCode >= 200 && $statusCode < 300) &&
                                          (stripos($contentType, 'xml') !== false ||
                                           stripos($responseBody, '<?xml') !== false ||
                                           stripos($responseBody, '<') !== false);

                        // Check if this is a successful empty response (common with RAAST)
                        $isSuccessfulEmpty = ($statusCode >= 200 && $statusCode < 300) &&
                                           (empty($responseBody) || strlen($responseBody) === 0);

                        // Log the response for debugging
                        \Log::info('RAAST API returned non-JSON response', [
                            'status_code' => $statusCode,
                            'json_error' => $jsonError,
                            'response_body' => substr($responseBody, 0, 1000), // First 1000 chars
                            'response_length' => strlen($responseBody),
                            'content_type' => $contentType,
                            'is_successful_xml' => $isSuccessfulXml
                        ]);

                        // Determine if this is a successful response
                        $isSuccessful = $isSuccessfulXml || $isSuccessfulEmpty;

                        // Create appropriate message based on response type
                        $message = '';
                        if ($isSuccessfulEmpty) {
                            $message = 'RAAST API returned successful empty response (HTTP 200)';
                        } elseif ($isSuccessfulXml) {
                            $message = 'RAAST API returned XML response';
                        } else {
                            $message = 'RAAST API returned non-JSON response';
                        }

                        // Create a structured response for non-JSON content
                        $responseArray = [
                            'error' => !$isSuccessful, // Not an error if it's successful XML or empty
                            'message' => $message,
                            'status_code' => $statusCode,
                            'json_error' => $jsonError,
                            'raw_response' => $responseBody, // Full response content
                            'raw_response_preview' => substr($responseBody, 0, 2000), // First 2000 chars for logging
                            'content_type' => $contentType,
                            'response_length' => strlen($responseBody),
                            'success' => $isSuccessful,
                            'response_headers' => $response->getHeaders(), // Include response headers
                            'is_empty_success' => $isSuccessfulEmpty, // Flag for empty successful responses
                            'is_xml_response' => $isSuccessfulXml // Flag for XML responses
                        ];

                        // Determine response type and handle accordingly
                        if ($isSuccessfulEmpty) {
                            $responseArray['response_type'] = 'empty_success';
                            $responseArray['raast_message_type'] = 'empty_response';
                            $responseArray['transaction_status'] = 'ACCP'; // Assume accepted for empty 200 response
                            $responseArray['success_reason'] = 'RAAST accepted request with empty response (common pattern)';
                        } elseif (stripos($responseBody, '<html') !== false || stripos($responseBody, '<!doctype') !== false) {
                            $responseArray['response_type'] = 'html';
                            // Try to extract title or error message from HTML
                            if (preg_match('/<title[^>]*>([^<]+)<\/title>/i', $responseBody, $matches)) {
                                $responseArray['html_title'] = trim($matches[1]);
                            }
                            if (preg_match('/<h1[^>]*>([^<]+)<\/h1>/i', $responseBody, $matches)) {
                                $responseArray['html_heading'] = trim($matches[1]);
                            }
                        } elseif (stripos($contentType, 'xml') !== false || stripos($responseBody, '<?xml') !== false || stripos($responseBody, '<') !== false) {
                            $responseArray['response_type'] = 'xml';

                            // Parse RAAST XML response using specialized parser
                            $xmlParseResult = $this->parseRaastXmlResponse($responseBody, $verbose);
                            $responseArray = array_merge($responseArray, $xmlParseResult);
                        } else {
                            $responseArray['response_type'] = 'unknown';
                        }
                    }

                    return [
                        'response' => $response,
                        'body' => $responseArray
                    ];
                });

                $response = $result['response'];
                $responseArray = $result['body'];

                // Add metadata
                $responseArray['_from_cache'] = false;
                $responseArray['_attempt'] = $attempt;
                $responseArray['_endpoint'] = $endpoint;

                if ($verbose) {
                    $this->info("✅ RAAST API Response received:");
                    $this->info("   Status: " . $response->getStatusCode());
                    $this->info("   Content-Type: " . $response->getHeaderLine('Content-Type'));
                    $this->info("   Content-Length: " . $response->getHeaderLine('Content-Length'));

                    // Get response body from the result array instead of undefined variable
                    $responseBodyLength = $responseArray['response_length'] ?? 0;
                    $rawResponse = $responseArray['raw_response'] ?? '';

                    $this->info("   Response Length: " . $responseBodyLength . " bytes");

                    // Show raw response if it's not too long or if debug mode
                    if ($responseBodyLength <= 1000 || $this->option('debug')) {
                        $this->info("   Raw Response:");
                        $this->line("--- RESPONSE START ---");
                        $this->line($rawResponse);
                        $this->line("--- RESPONSE END ---");
                    }

                    $this->info("   Parsed Response: " . json_encode($responseArray, JSON_PRETTY_PRINT));
                }

                return $responseArray;
            } catch (\GuzzleHttp\Exception\RequestException $ex) {
                $statusCode = $ex->hasResponse() ? $ex->getResponse()->getStatusCode() : 500;

                // Handle specific error codes with retry logic
                if (in_array($statusCode, [429, 500, 503]) && $attempt < $maxRetries) {
                    $this->warn("⚠️ RAAST API retry attempt {$attempt}/{$maxRetries}");
                    $this->info("   Status Code: {$statusCode}");
                    $this->info("   Retry Delay: {$retryDelay} seconds");

                    sleep($retryDelay);
                    $retryDelay *= 2; // Exponential backoff
                    continue;
                }

                $errorMessage = $this->getErrorMessage($statusCode, $ex->getMessage());

                if ($verbose) {
                    $this->error("❌ RAAST API Request Exception:");
                    $this->error("   Status Code: {$statusCode}");
                    $this->error("   Message: " . $ex->getMessage());
                    if ($ex->hasResponse()) {
                        $errorResponse = $ex->getResponse();
                        $errorBody = $errorResponse->getBody()->getContents();
                        $contentType = $errorResponse->getHeaderLine('Content-Type');

                        $this->error("   Error Content-Type: " . $contentType);
                        $this->error("   Error Response Length: " . strlen($errorBody) . " bytes");
                        $this->line(""); // Empty line for readability
                        $this->error("--- ERROR RESPONSE START ---");
                        $this->error($errorBody);
                        $this->error("--- ERROR RESPONSE END ---");
                        $this->line(""); // Empty line for readability
                    } else {
                        $this->error("   No response received from server");
                        $this->error("   This could indicate network connectivity issues");
                    }
                }

                $errorResult = [
                    'error' => true,
                    'message' => $errorMessage,
                    'status' => $statusCode,
                    'status_code' => $statusCode,
                    'attempt' => $attempt
                ];

                // Include response details if available
                if ($ex->hasResponse()) {
                    $errorResponse = $ex->getResponse();
                    $errorBody = $errorResponse->getBody()->getContents();
                    $errorResult['raw_response'] = $errorBody;
                    $errorResult['content_type'] = $errorResponse->getHeaderLine('Content-Type');
                    $errorResult['response_length'] = strlen($errorBody);
                } else {
                    $errorResult['raw_response'] = '';
                    $errorResult['content_type'] = '';
                    $errorResult['response_length'] = 0;
                    $errorResult['connection_error'] = true;
                }

                return $errorResult;
            } catch (\Exception $ex) {
                if ($attempt < $maxRetries) {
                    $this->warn("⚠️ RAAST API general exception, retrying... ({$attempt}/{$maxRetries})");
                    sleep($retryDelay);
                    $retryDelay *= 2;
                    continue;
                }

                if ($verbose) {
                    $this->error("❌ RAAST API General Exception:");
                    $this->error("   Message: " . $ex->getMessage());
                    $this->error("   File: " . $ex->getFile() . ":" . $ex->getLine());
                }

                return [
                    'error' => true,
                    'message' => 'RAAST API error: ' . $ex->getMessage(),
                    'status' => 500,
                    'attempt' => $attempt
                ];
            }
        }

        return [
            'error' => true,
            'message' => 'RAAST API failed after maximum retry attempts',
            'status' => 503
        ];
    }

    /**
     * Process test RTP request
     * Helper method for test scenarios
     */
    private function processTestRtp($testData, $verbose = false, $useRealIntegration = false)
    {
        if ($useRealIntegration) {
            // Use real RaastIntegrationService for customer registration and document signing
            $integrationService = app(RaastIntegrationService::class);
            $integrationResult = $integrationService->processRaastPayment([
                'document' => $testData['document'],
                'customer_cnic' => $testData['customer_cnic'],
                'customer_contact' => $testData['customer_contact'],
                'customer_name' => $testData['customer_name'],
            ]);

            if (isset($integrationResult['error'])) {
                return $integrationResult;
            } else {
                // Now make real RAAST API call with signed document
                $traceReference = (string) \Illuminate\Support\Str::uuid();
                $traceReference = str_replace("-", "", $traceReference);

                $requestData = [
                    'traceReference' => $traceReference,
                    'service' => 'rtp',
                    'type' => $testData['type'],
                    'sender' => $testData['sender'],
                    'receiver' => $testData['receiver'],
                    'document' => $integrationResult['data']['document'] // Use signed document
                ];

                $raastResponse = $this->callRaastApi($requestData, $verbose);

                if (isset($raastResponse['error']) && $raastResponse['error']) {
                    return [
                        'success' => false,
                        'error' => true,
                        'message' => 'RAAST API call failed: ' . ($raastResponse['message'] ?? 'Unknown error'),
                        'status' => $raastResponse['status'] ?? 500,
                        'integration_result' => $integrationResult,
                        'raast_response' => $raastResponse
                    ];
                } else {
                    return [
                        'success' => true,
                        'message' => 'RTP processed successfully via real RAAST API',
                        'data' => array_merge($integrationResult['data'], [
                            'trace_reference' => $traceReference,
                            'rtp_id' => 'RTP' . time() . rand(1000, 9999),
                            'raast_response' => $raastResponse
                        ]),
                        'integration_result' => $integrationResult,
                        'raast_response' => $raastResponse
                    ];
                }
            }
        } else {
            // Create a mock successful result for testing
            $rtpId = 'RTP' . time() . rand(1000, 9999);
            $traceReference = (string) \Illuminate\Support\Str::uuid();

            // Create RTP transaction record for testing
            $transaction = RaastTransaction::create([
                'trace_reference' => $traceReference,
                'service_type' => 'rtp',
                'message_type' => $testData['type'],
                'sender' => $testData['sender'],
                'receiver' => $testData['receiver'],
                'amount' => $testData['amount'],
                'currency' => $testData['currency'],
                'status' => 'completed',
                'request_data' => $testData,
                'response_data' => [
                    'mock_mode' => true,
                    'rtp_id' => $rtpId
                ],
                'ip_address' => '127.0.0.1'
            ]);

            return [
                'success' => true,
                'message' => 'RTP processed successfully (mock mode)',
                'data' => [
                    'rtp_created' => true,
                    'document_signed' => true,
                    'raast_response' => 'Mock RAAST RTP response for testing',
                    'processing_time' => '1.8 seconds',
                    'rtp_id' => $rtpId,
                    'trace_reference' => $traceReference,
                    'transaction_id' => $transaction->id,
                    'expiry_time' => now()->setTimezone('Asia/Karachi')->addMinutes(30)->format('Y-m-d\TH:i:s.vP'),
                    'payment_url' => 'https://raast.sbp.org.pk/pay/' . time()
                ],
                'mock_mode' => true,
                'jwt_authenticated' => true
            ];
        }
    }

    /**
     * Simulate bank payment for test scenarios
     * Helper method for test scenarios
     */
    private function simulateBankPayment($rtpResult, $paymentData, $verbose = false, $useRealIntegration = false)
    {
        if ($useRealIntegration) {
            // In real integration, we would make an API call to simulate bank payment
            // This is not possible in the test environment, so we'll just log it
            if ($verbose) {
                $this->info("📡 Would make real API call to simulate bank payment");
                $this->info("   RTP ID: " . ($rtpResult['data']['rtp_id'] ?? 'Unknown'));
                $this->info("   Amount: " . $paymentData['amount'] . " " . $paymentData['currency']);
            }

            // Return a simulated success response
            return [
                'success' => true,
                'message' => 'Bank payment simulated (real integration mode)',
                'data' => [
                    'payment_processed' => true,
                    'rtp_id' => $rtpResult['data']['rtp_id'] ?? 'Unknown',
                    'trace_reference' => $rtpResult['data']['trace_reference'] ?? 'Unknown',
                    'amount' => $paymentData['amount'],
                    'currency' => $paymentData['currency'],
                    'processing_time' => '1.5 seconds'
                ]
            ];
        } else {
            // In mock mode, we'll create a transaction record to simulate the payment
            $traceReference = (string) \Illuminate\Support\Str::uuid();

            // Create payment transaction record for testing
            $transaction = RaastTransaction::create([
                'trace_reference' => $traceReference,
                'service_type' => 'payment',
                'message_type' => 'pacs.008.001.08',
                'sender' => $paymentData['sender'],
                'receiver' => $paymentData['receiver'],
                'amount' => $paymentData['amount'],
                'currency' => $paymentData['currency'],
                'status' => 'completed',
                'request_data' => [
                    'rtp_reference' => $rtpResult['data']['rtp_id'] ?? 'Unknown',
                    'payment_data' => $paymentData
                ],
                'response_data' => [
                    'mock_mode' => true,
                    'payment_id' => 'PAY' . time() . rand(1000, 9999)
                ],
                'ip_address' => '127.0.0.1'
            ]);

            return [
                'success' => true,
                'message' => 'Bank payment simulated (mock mode)',
                'data' => [
                    'payment_processed' => true,
                    'rtp_id' => $rtpResult['data']['rtp_id'] ?? 'Unknown',
                    'trace_reference' => $traceReference,
                    'transaction_id' => $transaction->id,
                    'amount' => $paymentData['amount'],
                    'currency' => $paymentData['currency'],
                    'processing_time' => '1.5 seconds'
                ],
                'mock_mode' => true
            ];
        }
    }

    /**
     * Get user-friendly error message based on status code
     */
    private function getErrorMessage($statusCode, $originalMessage)
    {
        switch ($statusCode) {
            case 400:
                return 'Bad request - Invalid payment data';
            case 401:
                return 'Unauthorized - Invalid bearer token';
            case 403:
                return 'Forbidden - Access denied';
            case 404:
                return 'Not found - RAAST endpoint not available';
            case 408:
                return 'Request timeout - RAAST server did not respond in time';
            case 429:
                return 'Too many requests - Rate limit exceeded';
            case 500:
                return 'Internal server error - RAAST server error';
            case 502:
                return 'Bad gateway - RAAST server unavailable';
            case 503:
                return 'Service unavailable - RAAST server temporarily down';
            case 504:
                return 'Gateway timeout - RAAST server timeout';
            default:
                return "RAAST API error ({$statusCode}): {$originalMessage}";
        }
    }

    /**
     * Test expiration scenarios
     */
    private function testExpirationScenarios($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🧪 RTP Expiration Test Scenarios");
        $this->info("=" . str_repeat('=', 60));

        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE - Some scenarios may not be fully testable");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION");
        }

        // Test RTP expiration
        $this->testRtpExpiration($verbose, $useRealIntegration);

        // Test invalid expiration time
        $this->testInvalidExpirationTime($verbose, $useRealIntegration);

        $this->info("\n🎉 Expiration Testing Complete");
    }

    /**
     * Test status request scenarios
     */
    private function testStatusRequestScenarios($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🧪 RTP Status Request Test Scenarios");
        $this->info("=" . str_repeat('=', 60));

        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE - Some scenarios may not be fully testable");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION");
        }

        // Test RTP status requests
        $this->testRtpStatusRequests($verbose, $useRealIntegration);

        $this->info("\n🎉 Status Request Testing Complete");
    }

    /**
     * Test error flow scenarios
     */
    private function testErrorFlowScenarios($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🧪 Error Flow Test Scenarios");
        $this->info("=" . str_repeat('=', 60));

        if (!$useRealIntegration) {
            $this->warn("⚠️  Running in MOCK MODE - Some scenarios may not be fully testable");
        } else {
            $this->info("🔗 Using REAL RAAST API INTEGRATION");
        }

        // Test duplicate RTP references
        $this->testDuplicateRtpReferences($verbose, $useRealIntegration);

        // Test payment after expiration
        $this->testPaymentAfterExpiration($verbose, $useRealIntegration);

        // Test non-existent RTP reference
        $this->testNonExistentRtpReference($verbose, $useRealIntegration);

        $this->info("\n🎉 Error Flow Testing Complete");
    }

    /**
     * Generate a sample RAAST access token
     */
    private function generateAccessToken()
    {
        $this->info("\n🔑 RAAST Access Token Generator");
        $this->info("=" . str_repeat('=', 60));

        try {
            $oauth2Service = app(\App\Services\RaastOAuth2Service::class);
            $sampleToken = $oauth2Service->generateSampleAccessToken();

            $this->info("\n✅ Sample Access Token Generated Successfully!");
            $this->info("\n📋 Token Details:");

            // Validate the generated token
            $validation = $oauth2Service->validateClientToken($sampleToken);

            if ($validation['valid']) {
                $this->info("✅ Generated token passes basic validation");
            } else {
                $this->warn("⚠️ Generated token has validation issues:");
                foreach ($validation['issues'] as $issue) {
                    $this->warn("   • " . $issue);
                }
            }

            $claims = $validation['claims'];
            $this->info("\n📝 Token Claims:");
            $this->info("   iss (Issuer): " . ($claims['iss'] ?? 'Missing'));
            $this->info("   sub (Subject): " . ($claims['sub'] ?? 'Missing (REQUIRED)'));
            $this->info("   jti (JWT ID): " . ($claims['jti'] ?? 'Missing (REQUIRED)'));
            $this->info("   iat (Issued At): " . (isset($claims['iat']) ? date('Y-m-d H:i:s', $claims['iat']) : 'Missing'));
            $this->info("   exp (Expires): " . (isset($claims['exp']) ? date('Y-m-d H:i:s', $claims['exp']) : 'Missing'));
            $this->info("   asrv_type: " . ($claims['asrv_type'] ?? 'Missing'));
            $this->info("   asrv_cert_iss: " . ($claims['asrv_cert_iss'] ?? 'Missing'));
            $this->info("   asrv_cert_sn: " . ($claims['asrv_cert_sn'] ?? 'Missing'));

            $this->info("\n🔗 Generated Access Token:");
            $this->info($sampleToken);

            $this->info("\n📝 To use this token:");
            $this->info("1. Copy the token above");
            $this->info("2. Update your .env file:");
            $this->info("   RAAST_BEARER_TOKEN=" . $sampleToken);
            $this->info("3. Clear config cache: php artisan config:clear");
            $this->info("4. Test authentication: php artisan raast:test-e2e --test-auth");

            $this->warn("\n⚠️ Important Notes:");
            $this->warn("• This is a sample token for testing purposes only");
            $this->warn("• In production, get the actual access token from RAAST OAuth2 flow");
            $this->warn("• This token has asrv_type: 'access' (required for API calls)");
            $this->warn("• Update the certificate serial number (asrv_cert_sn) with your actual certificate");

            $this->info("\n💡 Difference from Client Token:");
            $this->info("• Client Token: asrv_type = 'client' (used for OAuth2 authentication)");
            $this->info("• Access Token: asrv_type = 'access' (used for API calls)");
        } catch (\Exception $e) {
            $this->error("❌ Failed to generate access token: " . $e->getMessage());
        }
    }

    /**
     * Generate a sample RAAST client token
     */
    private function generateClientToken()
    {
        $this->info("\n🔑 RAAST Client Token Generator");
        $this->info("=" . str_repeat('=', 60));

        try {
            $oauth2Service = app(\App\Services\RaastOAuth2Service::class);
            $sampleToken = $oauth2Service->generateSampleClientToken();

            $this->info("\n✅ Sample Client Token Generated Successfully!");
            $this->info("\n📋 Token Details:");

            // Validate the generated token
            $validation = $oauth2Service->validateClientToken($sampleToken);

            if ($validation['valid']) {
                $this->info("✅ Generated token passes RAAST validation");
            } else {
                $this->warn("⚠️ Generated token has validation issues:");
                foreach ($validation['issues'] as $issue) {
                    $this->warn("   • " . $issue);
                }
            }

            $claims = $validation['claims'];
            $this->info("\n📝 Token Claims:");
            $this->info("   iss (Issuer): " . ($claims['iss'] ?? 'Missing'));
            $this->info("   sub (Subject): " . ($claims['sub'] ?? 'Missing (REQUIRED)'));
            $this->info("   jti (JWT ID): " . ($claims['jti'] ?? 'Missing (REQUIRED)'));
            $this->info("   iat (Issued At): " . (isset($claims['iat']) ? date('Y-m-d H:i:s', $claims['iat']) : 'Missing'));
            $this->info("   exp (Expires): " . (isset($claims['exp']) ? date('Y-m-d H:i:s', $claims['exp']) : 'Missing'));
            $this->info("   asrv_type: " . ($claims['asrv_type'] ?? 'Missing'));
            $this->info("   asrv_cert_iss: " . ($claims['asrv_cert_iss'] ?? 'Missing'));
            $this->info("   asrv_cert_sn: " . ($claims['asrv_cert_sn'] ?? 'Missing'));

            $this->info("\n🔗 Generated Token:");
            $this->info($sampleToken);

            $this->info("\n📝 To use this token:");
            $this->info("1. Copy the token above");
            $this->info("2. Update your .env file:");
            $this->info("   RAAST_CLIENT_TOKEN=" . $sampleToken);
            $this->info("3. Clear config cache: php artisan config:clear");
            $this->info("4. Test authentication: php artisan raast:test-e2e --test-auth");

            $this->warn("\n⚠️ Important Notes:");
            $this->warn("• This is a sample token for testing purposes only");
            $this->warn("• In production, generate a properly signed JWT with your private key");
            $this->warn("• Update the certificate serial number (asrv_cert_sn) with your actual certificate");
            $this->warn("• Ensure the certificate issuer (asrv_cert_iss) matches your environment");
        } catch (\Exception $e) {
            $this->error("❌ Failed to generate client token: " . $e->getMessage());
        }
    }

    /**
     * Test RAAST API authentication
     */
    private function testRaastAuthentication($verbose = false)
    {
        $this->info("\n🔐 RAAST API Authentication Testing");
        $this->info("=" . str_repeat('=', 60));

        // Test 1: Check configuration
        $this->info("\n📋 Test 1: Configuration Check");
        $endpoint = config('services.raast.endpoint');
        $bearerToken = config('services.raast.bearer_token');
        $clientToken = config('services.raast.client_token');

        $this->info("🎯 RAAST Endpoint: " . ($endpoint ?: 'NOT CONFIGURED'));
        $this->info("🔑 Bearer Token: " . ($bearerToken ? 'Configured (' . strlen($bearerToken) . ' chars)' : 'NOT CONFIGURED'));
        $this->info("🔑 Client Token: " . ($clientToken ? 'Configured (' . strlen($clientToken) . ' chars)' : 'NOT CONFIGURED'));

        if (!$endpoint) {
            $this->error("❌ RAAST endpoint is not configured");
            $this->info("   Add RAAST_ENDPOINT to your .env file");
            return;
        }

        // Check if we have either bearer token or client token
        if (!$bearerToken && !$clientToken) {
            $this->error("❌ No RAAST authentication token is configured");
            $this->info("   Add either RAAST_BEARER_TOKEN or RAAST_CLIENT_TOKEN to your .env file");
            return;
        }

        if ($bearerToken && $bearerToken === 'your_bearer_token_here') {
            $this->warn("⚠️ RAAST bearer token is using placeholder value");
            $this->info("   Replace 'your_bearer_token_here' with actual token in .env file");
        }

        // Determine authentication method
        $authMethod = $clientToken ? 'OAuth2 (Client Token)' : 'Direct Bearer Token';
        $this->info("🔐 Authentication Method: " . $authMethod);

        // Test 2: Token format validation
        $this->info("\n🔍 Test 2: Token Format Validation");

        $tokenToValidate = $clientToken ?: $bearerToken;
        $tokenType = $clientToken ? 'Client Token' : 'Bearer Token';

        if ($tokenToValidate) {
            $tokenLength = strlen($tokenToValidate);
            $this->info("   {$tokenType} Length: {$tokenLength} characters");

            if ($tokenLength < 10) {
                $this->warn("   ⚠️ Token seems too short (< 10 chars)");
            } elseif ($tokenLength > 1000) {
                $this->warn("   ⚠️ Token seems too long (> 1000 chars)");
            } else {
                $this->info("   ✅ Token length appears reasonable");
            }

            // Check for JWT format
            if (substr_count($tokenToValidate, '.') === 2) {
                $this->info("   ✅ Token appears to be in JWT format");

                // Try to decode JWT
                try {
                    $parts = explode('.', $tokenToValidate);
                    $payload = json_decode(base64_decode($parts[1]), true);

                    if ($payload) {
                        $this->info("   ✅ JWT payload decoded successfully");
                        $this->info("      Subject: " . ($payload['sub'] ?? 'Unknown'));
                        $this->info("      Issued At: " . (isset($payload['iat']) ? date('Y-m-d H:i:s', $payload['iat']) : 'Unknown'));
                        $this->info("      Expires At: " . (isset($payload['exp']) ? date('Y-m-d H:i:s', $payload['exp']) : 'Unknown'));
                        $this->info("      ASRV Type: " . ($payload['asrv_type'] ?? 'Missing'));
                        $this->info("      ASRV Cert Issuer: " . ($payload['asrv_cert_iss'] ?? 'Unknown'));

                        if (!isset($payload['asrv_type'])) {
                            $this->warn("   ⚠️ Token missing 'asrv_type' claim (required by RAAST)");
                        }
                    }
                } catch (\Exception $e) {
                    $this->warn("   ⚠️ Failed to decode JWT: " . $e->getMessage());
                }
            } else {
                $this->warn("   ⚠️ Token does not appear to be in JWT format");
            }
        }

        // Test 3: Authentication header construction
        $this->info("\n🔧 Test 3: Authentication Header Construction");
        $authHeader = 'Bearer ' . $bearerToken;
        $this->info("   Authorization Header: Bearer " . substr($bearerToken, 0, 10) . '...');
        $this->info("   Full Header Length: " . strlen($authHeader) . " characters");

        // Test 4: Mock authentication test
        $this->info("\n🧪 Test 4: Mock Authentication Test");
        $this->info("   Testing header format with mock request...");

        try {
            $headers = [
                'Authorization' => $authHeader,
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Timestamp' => now()->setTimezone('Asia/Karachi')->format('Y-m-d\TH:i:s.vP'),
                'User-Agent' => 'Islamabad-City-App/1.0'
            ];

            $this->info("   ✅ Headers constructed successfully");
            if ($verbose) {
                $this->info("   Headers:");
                foreach ($headers as $key => $value) {
                    if ($key === 'Authorization') {
                        $this->info("     {$key}: Bearer " . substr($bearerToken, 0, 10) . '...');
                    } else {
                        $this->info("     {$key}: {$value}");
                    }
                }
            }
        } catch (\Exception $e) {
            $this->error("   ❌ Header construction failed: " . $e->getMessage());
        }

        // Test 5: Client Token Validation (RAAST Requirements)
        $this->info("\n🔍 Test 5: Client Token Validation (RAAST Requirements)");

        $clientToken = config('services.raast.client_token');
        if ($clientToken) {
            try {
                $oauth2Service = app(\App\Services\RaastOAuth2Service::class);
                $validation = $oauth2Service->validateClientToken($clientToken);

                if ($validation['valid']) {
                    $this->info("   ✅ Client token is valid according to RAAST requirements");
                } else {
                    $this->error("   ❌ Client token validation failed:");
                    foreach ($validation['issues'] as $issue) {
                        $this->error("      • " . $issue);
                    }
                }

                $this->info("   📋 Client Token Claims:");
                $claims = $validation['claims'];
                $this->info("      iss (Issuer): " . ($claims['iss'] ?? 'Missing'));
                $this->info("      sub (Subject): " . ($claims['sub'] ?? 'Missing (REQUIRED)'));
                $this->info("      jti (JWT ID): " . ($claims['jti'] ?? 'Missing (REQUIRED)'));
                $this->info("      iat (Issued At): " . (isset($claims['iat']) ? date('Y-m-d H:i:s', $claims['iat']) : 'Missing'));
                $this->info("      exp (Expires): " . (isset($claims['exp']) ? date('Y-m-d H:i:s', $claims['exp']) : 'Missing'));
                $this->info("      asrv_type: " . ($claims['asrv_type'] ?? 'Missing (REQUIRED)'));
                $this->info("      asrv_cert_iss: " . ($claims['asrv_cert_iss'] ?? 'Missing (REQUIRED)'));
                $this->info("      asrv_cert_sn: " . ($claims['asrv_cert_sn'] ?? 'Missing (RECOMMENDED)'));
            } catch (\Exception $e) {
                $this->error("   ❌ Client token validation failed: " . $e->getMessage());
            }
        } else {
            $this->warn("   ⚠️ Client token not configured - skipping validation");
        }

        // Test 6: OAuth2 Access Token Generation
        $this->info("\n🔄 Test 6: OAuth2 Access Token Generation");

        if ($clientToken) {
            try {
                $oauth2Service = app(\App\Services\RaastOAuth2Service::class);

                $this->info("   🚀 Attempting to get access token using password grant...");
                $this->info("      Grant Type: password");
                $this->info("      Username: " . config('services.raast.client_id'));
                $this->info("      Password: " . (config('services.raast.client_secret') ? '[CONFIGURED]' : '[NOT CONFIGURED]'));

                $accessToken = $oauth2Service->getAccessToken();

                if ($accessToken) {
                    $accessTokenInfo = $oauth2Service->getTokenInfo($accessToken);
                    $this->info("   ✅ Access token obtained successfully!");
                    $this->info("      Type: " . ($accessTokenInfo['asrv_type'] ?? 'Unknown'));
                    $this->info("      Expires: " . ($accessTokenInfo['expires_at'] ?? 'Unknown'));
                    $this->info("      Time to expiry: " . ($accessTokenInfo['time_to_expiry'] ?? 0) . ' seconds');
                } else {
                    $this->error("   ❌ Failed to obtain access token");
                    $this->info("   📝 Check logs for detailed error information");
                    $this->info("   💡 Common issues:");
                    $this->info("      • RAAST server not reachable");
                    $this->info("      • Invalid client credentials");
                    $this->info("      • Client token missing required claims");
                }
            } catch (\Exception $e) {
                $this->error("   ❌ OAuth2 test failed: " . $e->getMessage());
            }
        } else {
            $this->warn("   ⚠️ Client token not configured - skipping OAuth2 test");
        }

        // Test 7: Recommendations
        $this->info("\n💡 Test 7: Recommendations");

        if (!$clientToken) {
            $this->warn("   🔧 Action Required: Configure RAAST client token");
            $this->info("   📝 Steps:");
            $this->info("      1. Generate a client token with required RAAST claims:");
            $this->info("         • iss: Your participant code (e.g., NITBPKKA)");
            $this->info("         • iat: Current timestamp");
            $this->info("         • exp: Expiration timestamp");
            $this->info("         • asrv_type: 'client'");
            $this->info("         • asrv_cert_iss: Certificate issuer");
            $this->info("         • asrv_cert_sn: Certificate serial number (optional)");
            $this->info("      2. Add RAAST_CLIENT_TOKEN to your .env file");
            $this->info("      3. Restart your application");
        } else {
            $oauth2Service = app(\App\Services\RaastOAuth2Service::class);
            $validation = $oauth2Service->validateClientToken($clientToken);

            if ($validation['valid']) {
                $this->info("   ✅ Client token is properly configured");
                $this->info("   📝 Next steps:");
                $this->info("      1. Test connectivity: php artisan raast:test-e2e --test-connectivity");
                $this->info("      2. Test real API call: php artisan raast:test-e2e --real-integration");
            } else {
                $this->warn("   🔧 Action Required: Fix client token issues");
                $this->info("   📝 Issues to resolve:");
                foreach ($validation['issues'] as $issue) {
                    $this->info("      • " . $issue);
                }
                $this->info("   📝 Steps:");
                $this->info("      1. Generate a new client token with all required claims");
                $this->info("      2. Update RAAST_CLIENT_TOKEN in your .env file");
                $this->info("      3. Test again: php artisan raast:test-e2e --test-auth");
            }
        }

        $this->info("\n🎉 RAAST API Authentication Testing Complete");
    }

    /**
     * Test RAAST API connectivity
     */
    private function testRaastConnectivity($verbose = false)
    {
        $this->info("\n🌐 RAAST API Connectivity Testing");
        $this->info("=" . str_repeat('=', 60));

        $endpoint = config('services.raast.endpoint');
        $parsedUrl = parse_url($endpoint);
        $host = $parsedUrl['host'] ?? 'unknown';
        $port = $parsedUrl['port'] ?? ($parsedUrl['scheme'] === 'https' ? 443 : 80);
        $scheme = $parsedUrl['scheme'] ?? 'http';

        $this->info("🎯 Target Endpoint: {$endpoint}");
        $this->info("   Host: {$host}");
        $this->info("   Port: {$port}");
        $this->info("   Scheme: {$scheme}");

        // Test 1: Basic TCP connectivity
        $this->info("\n🔌 Test 1: TCP Connectivity");
        $tcpResult = $this->testTcpConnectivity($host, $port, $verbose);

        // Test 2: SSL/TLS connectivity (if HTTPS)
        if ($scheme === 'https') {
            $this->info("\n🔒 Test 2: SSL/TLS Connectivity");
            $sslResult = $this->testSslConnectivity($host, $port, $verbose);
        }

        // Test 3: HTTP connectivity with different SSL settings
        $this->info("\n📡 Test 3: HTTP Connectivity Tests");
        $this->testHttpConnectivity($endpoint, $verbose);

        // Test 4: DNS resolution
        $this->info("\n🌍 Test 4: DNS Resolution");
        $this->testDnsResolution($host, $verbose);

        $this->info("\n🎉 RAAST API Connectivity Testing Complete");
    }

    /**
     * Test TCP connectivity to host:port
     */
    private function testTcpConnectivity($host, $port, $verbose = false): bool
    {
        try {
            $timeout = 10;
            $socket = @fsockopen($host, $port, $errno, $errstr, $timeout);

            if ($socket) {
                fclose($socket);
                $this->info("✅ TCP connection to {$host}:{$port} successful");
                return true;
            } else {
                $this->error("❌ TCP connection to {$host}:{$port} failed");
                $this->error("   Error {$errno}: {$errstr}");
                return false;
            }
        } catch (\Exception $e) {
            $this->error("❌ TCP connection test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test SSL/TLS connectivity
     */
    private function testSslConnectivity($host, $port, $verbose = false): bool
    {
        try {
            $context = stream_context_create([
                'ssl' => [
                    'verify_peer' => false,
                    'verify_peer_name' => false,
                    'allow_self_signed' => true,
                ]
            ]);

            $socket = @stream_socket_client(
                "ssl://{$host}:{$port}",
                $errno,
                $errstr,
                10,
                STREAM_CLIENT_CONNECT,
                $context
            );

            if ($socket) {
                fclose($socket);
                $this->info("✅ SSL/TLS connection to {$host}:{$port} successful");
                return true;
            } else {
                $this->error("❌ SSL/TLS connection to {$host}:{$port} failed");
                $this->error("   Error {$errno}: {$errstr}");
                return false;
            }
        } catch (\Exception $e) {
            $this->error("❌ SSL/TLS connection test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Test HTTP connectivity with different SSL configurations
     */
    private function testHttpConnectivity($endpoint, $verbose = false)
    {
        $configurations = [
            'SSL Verification Disabled' => [
                'verify' => false,
                'curl' => [
                    CURLOPT_SSL_VERIFYPEER => false,
                    CURLOPT_SSL_VERIFYHOST => 0,
                ]
            ],
            'SSL Verification Enabled' => [
                'verify' => true,
                'curl' => [
                    CURLOPT_SSL_VERIFYPEER => true,
                    CURLOPT_SSL_VERIFYHOST => 2,
                ]
            ]
        ];

        foreach ($configurations as $configName => $config) {
            $this->info("\n   Testing: {$configName}");

            try {
                $client = new \GuzzleHttp\Client(array_merge([
                    'timeout' => 10,
                    'connect_timeout' => 5,
                ], $config));

                $response = $client->get($endpoint);
                $statusCode = $response->getStatusCode();

                $this->info("   ✅ HTTP request successful (Status: {$statusCode})");

                if ($verbose) {
                    $this->info("   Response headers: " . json_encode($response->getHeaders()));
                }
            } catch (\GuzzleHttp\Exception\ConnectException $e) {
                $this->error("   ❌ Connection failed: " . $e->getMessage());
            } catch (\GuzzleHttp\Exception\RequestException $e) {
                $statusCode = $e->hasResponse() ? $e->getResponse()->getStatusCode() : 'N/A';
                $this->warn("   ⚠️ HTTP request failed (Status: {$statusCode}): " . $e->getMessage());
            } catch (\Exception $e) {
                $this->error("   ❌ Unexpected error: " . $e->getMessage());
            }
        }
    }

    /**
     * Test DNS resolution
     */
    private function testDnsResolution($host, $verbose = false)
    {
        try {
            $ip = gethostbyname($host);

            if ($ip === $host) {
                $this->error("❌ DNS resolution failed for {$host}");
            } else {
                $this->info("✅ DNS resolution successful: {$host} → {$ip}");

                if ($verbose) {
                    // Try to get additional DNS records
                    $records = dns_get_record($host, DNS_A);
                    if ($records) {
                        $this->info("   DNS A records:");
                        foreach ($records as $record) {
                            $this->info("     " . $record['ip']);
                        }
                    }
                }
            }
        } catch (\Exception $e) {
            $this->error("❌ DNS resolution test failed: " . $e->getMessage());
        }
    }

    /**
     * Test XML Digital Signature functionality
     */
    private function testXmlDigitalSignature($verbose = false)
    {
        $this->info("\n🔐 XML Digital Signature Testing");
        $this->info("=" . str_repeat('=', 60));

        try {
            // Test 1: Generate XML with signature
            $this->info("\n📝 Test 1: Generate RTP XML with Digital Signature");
            $messageId = 'SIG_TEST_' . time();
            $xmlWithSignature = $this->generateTestRtpXml($messageId, true);

            if ($verbose) {
                $this->info("Generated XML (first 500 chars):");
                $this->info(substr($xmlWithSignature, 0, 500) . "...");
            }

            // Test 2: Validate signature structure
            $this->info("\n🔍 Test 2: Validate Signature Structure");
            $signatureService = app(XmlDigitalSignatureService::class);
            $validation = $signatureService->validateSignatureStructure($xmlWithSignature);

            if (isset($validation['error'])) {
                $this->error("❌ Signature validation failed: " . $validation['error']);
            } else {
                $this->info("✅ Signature Structure Validation Results:");
                $this->info("   • Has Sgntr element: " . ($validation['has_sgntr_element'] ? '✅' : '❌'));
                $this->info("   • Has Signature element: " . ($validation['has_signature'] ? '✅' : '❌'));
                $this->info("   • Has SignedInfo: " . ($validation['has_signed_info'] ? '✅' : '❌'));
                $this->info("   • Has SignatureValue: " . ($validation['has_signature_value'] ? '✅' : '❌'));
                $this->info("   • Has KeyInfo: " . ($validation['has_key_info'] ? '✅' : '❌'));
                $this->info("   • Has QualifyingProperties: " . ($validation['has_qualifying_properties'] ? '✅' : '❌'));
                $this->info("   • Reference count: " . $validation['reference_count']);

                if ($validation['has_signature'] && $validation['has_signed_info'] && $validation['has_signature_value']) {
                    $this->info("✅ Digital signature structure is compliant with XMLDSig specification");
                } else {
                    $this->warn("⚠️ Digital signature structure has missing elements");
                }
            }

            // Test 3: Generate XML without signature for comparison
            $this->info("\n📝 Test 3: Generate RTP XML without Digital Signature");
            $xmlWithoutSignature = $this->generateTestRtpXml($messageId, false);
            $validationWithoutSig = $signatureService->validateSignatureStructure($xmlWithoutSignature);

            $this->info("✅ XML without signature validation:");
            $this->info("   • Has Signature element: " . ($validationWithoutSig['has_signature'] ? '✅' : '❌'));

            // Test 4: Compare XML sizes
            $this->info("\n📊 Test 4: XML Size Comparison");
            $sizeWithSignature = strlen($xmlWithSignature);
            $sizeWithoutSignature = strlen($xmlWithoutSignature);
            $sizeDifference = $sizeWithSignature - $sizeWithoutSignature;

            $this->info("   • XML with signature: " . number_format($sizeWithSignature) . " bytes");
            $this->info("   • XML without signature: " . number_format($sizeWithoutSignature) . " bytes");
            $this->info("   • Signature overhead: " . number_format($sizeDifference) . " bytes (" .
                round(($sizeDifference / $sizeWithoutSignature) * 100, 1) . "% increase)");

            // Test 5: Signature components validation
            $this->info("\n🔍 Test 5: Signature Components Validation");
            if (isset($validation['has_signature']) && $validation['has_signature']) {
                $this->info("✅ XMLDSig Signature Components:");
                $this->info("   • Canonicalization Method: Exclusive C14N");
                $this->info("   • Signature Method: RSA-SHA1");
                $this->info("   • Digest Method: SHA256");
                $this->info("   • References: " . $validation['reference_count'] . " (Expected: 3)");

                if ($validation['reference_count'] === 3) {
                    $this->info("   ✅ Correct number of references (KeyInfo, SignedProperties, Document)");
                } else {
                    $this->warn("   ⚠️ Unexpected number of references");
                }

                if (isset($validation['has_qualifying_properties']) && $validation['has_qualifying_properties']) {
                    $this->info("   ✅ XAdES QualifyingProperties present");
                    $this->info("   ✅ SigningTime included");
                } else {
                    $this->warn("   ⚠️ XAdES QualifyingProperties missing");
                }
            } else {
                $this->warn("⚠️ No signature found for component validation");
            }

            // Test 6: XML Well-formedness validation
            $this->info("\n🔍 Test 6: XML Well-formedness Validation");
            if ($this->validateXmlWellFormedness($xmlWithSignature)) {
                $this->info("✅ XML with signature is well-formed");
            } else {
                $this->error("❌ XML with signature is malformed");
            }

            if ($this->validateXmlWellFormedness($xmlWithoutSignature)) {
                $this->info("✅ XML without signature is well-formed");
            } else {
                $this->error("❌ XML without signature is malformed");
            }

            $this->info("\n🎉 XML Digital Signature Testing Complete");
        } catch (\Exception $e) {
            $this->error("❌ XML Digital Signature Testing Failed: " . $e->getMessage());
            if ($verbose) {
                $this->error("Stack trace: " . $e->getTraceAsString());
            }
        }
    }

    /**
     * Validate XML well-formedness
     */
    private function validateXmlWellFormedness(string $xmlContent): bool
    {
        try {
            $doc = new \DOMDocument();
            libxml_use_internal_errors(true);
            libxml_clear_errors();

            $result = $doc->loadXML($xmlContent);

            if (!$result) {
                $errors = libxml_get_errors();
                foreach ($errors as $error) {
                    $this->error("   XML Error - Line {$error->line}, Column {$error->column}: {$error->message}");
                }
                libxml_clear_errors();
                return false;
            }

            libxml_clear_errors();
            return true;
        } catch (\Exception $e) {
            $this->error("   XML Validation Exception: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Placeholder methods for test scenarios (to be implemented)
     */
    private function testRtpExpiration($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test: RTP Expiration Timeout");
        $this->info("   • MSP sends Pay Now RTP");
        $this->info("   • Bank doesn't send payment within expiration time");
        $this->info("   Expected: MSP receives pain.014 (RTP status, RJCT)");
        $this->warn("⚠️  Test scenario placeholder - implementation needed");
    }

    private function testInvalidExpirationTime($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test: Invalid Expiration Time");
        $this->info("   • MSP sends Pay Now RTP with invalid expiration");
        $this->info("   Expected: MSP receives admi.002, RJCT");
        $this->warn("⚠️  Test scenario placeholder - implementation needed");
    }

    private function testRtpStatusRequests($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test: RTP Status Requests");
        $this->info("   • Test status requests for paid, rejected, and accepted RTPs");
        $this->info("   Expected: Appropriate pain.014 responses");
        $this->warn("⚠️  Test scenario placeholder - implementation needed");
    }

    private function testDuplicateRtpReferences($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test: Duplicate RTP References");
        $this->info("   • MSP sends two RTPs with same reference number");
        $this->info("   Expected: Second RTP receives admi.002, RJCT");
        $this->warn("⚠️  Test scenario placeholder - implementation needed");
    }

    private function testPaymentAfterExpiration($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test: Payment After Expiration");
        $this->info("   • Bank sends payment after RTP expiration");
        $this->info("   Expected: Bank receives pacs.002 (RJCT)");
        $this->warn("⚠️  Test scenario placeholder - implementation needed");
    }

    private function testNonExistentRtpReference($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🔄 Test: Non-existent RTP Reference");
        $this->info("   • Bank sends payment with reference to non-existent RTP");
        $this->info("   Expected: Bank receives pacs.002 (RJCT)");
        $this->warn("⚠️  Test scenario placeholder - implementation needed");
    }

    /**
     * Test non-JSON response handling
     */
    private function testNonJsonResponseHandling()
    {
        $this->info('🧪 Testing Non-JSON Response Handling');

        // Test HTML error response
        $htmlResponse = '<html><head><title>502 Bad Gateway</title></head><body><h1>Bad Gateway</h1><p>The proxy server received an invalid response.</p></body></html>';
        $this->testJsonParsing($htmlResponse, 'HTML Error Page');

        // Test XML response
        $xmlResponse = '<?xml version="1.0"?><error><code>500</code><message>Internal Server Error</message></error>';
        $this->testJsonParsing($xmlResponse, 'XML Error Response');

        // Test plain text response
        $textResponse = 'Connection timeout. Please try again later.';
        $this->testJsonParsing($textResponse, 'Plain Text Response');

        // Test empty response
        $emptyResponse = '';
        $this->testJsonParsing($emptyResponse, 'Empty Response');
    }

    /**
     * Test JSON parsing with different response types
     */
    private function testJsonParsing($responseBody, $testName)
    {
        $this->line("  Testing: {$testName}");

        $responseArray = json_decode($responseBody, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            $jsonError = json_last_error_msg();
            $statusCode = 500;

            // Create a structured response for non-JSON content
            $responseArray = [
                'error' => true,
                'message' => 'RAAST API returned non-JSON response',
                'status_code' => $statusCode,
                'json_error' => $jsonError,
                'raw_response' => substr($responseBody, 0, 200), // Limit for display
                'response_length' => strlen($responseBody)
            ];

            // If it's an HTML error page, try to extract useful information
            if (stripos($responseBody, '<html') !== false || stripos($responseBody, '<!doctype') !== false) {
                $responseArray['response_type'] = 'html';
                if (preg_match('/<title[^>]*>([^<]+)<\/title>/i', $responseBody, $matches)) {
                    $responseArray['html_title'] = trim($matches[1]);
                }
                if (preg_match('/<h1[^>]*>([^<]+)<\/h1>/i', $responseBody, $matches)) {
                    $responseArray['html_heading'] = trim($matches[1]);
                }
            } elseif (stripos($responseBody, '<?xml') !== false) {
                $responseArray['response_type'] = 'xml';
            } else {
                $responseArray['response_type'] = 'unknown';
            }

            $this->line("    ✅ Handled as: {$responseArray['response_type']}");
            if (isset($responseArray['html_title'])) {
                $this->line("    📄 Title: {$responseArray['html_title']}");
            }
            if (isset($responseArray['html_heading'])) {
                $this->line("    📋 Heading: {$responseArray['html_heading']}");
            }
        } else {
            $this->line("    ✅ Valid JSON response");
        }
    }

    /**
     * Test XML response handling scenarios
     */
    private function testXmlResponseHandling()
    {
        $this->info('🧪 Testing XML Response Handling Scenarios');

        $scenarios = [
            [
                'name' => 'Successful XML Response',
                'status_code' => 200,
                'content_type' => 'application/xml',
                'response_body' => '<?xml version="1.0"?><response><status>success</status><message>RTP created successfully</message><transactionId>TXN123456</transactionId></response>'
            ],
            [
                'name' => 'Empty Successful Response',
                'status_code' => 200,
                'content_type' => '',
                'response_body' => ''
            ],
            [
                'name' => 'HTML Error Response',
                'status_code' => 500,
                'content_type' => 'text/html',
                'response_body' => '<html><head><title>Internal Server Error</title></head><body><h1>Server Error</h1></body></html>'
            ],
            [
                'name' => 'XML Error Response',
                'status_code' => 400,
                'content_type' => 'application/xml',
                'response_body' => '<?xml version="1.0"?><error><code>400</code><message>Invalid request format</message></error>'
            ],
            [
                'name' => 'RAAST XML Success Response',
                'status_code' => 200,
                'content_type' => 'application/xml',
                'response_body' => '<?xml version="1.0"?><DataPDU><Body><AppHdr><BizMsgIdr>MSG123</BizMsgIdr></AppHdr><Document><status>ACCP</status></Document></Body></DataPDU>'
            ]
        ];

        foreach ($scenarios as $scenario) {
            $this->line("\n  📋 Testing: {$scenario['name']}");
            $this->testXmlResponseScenario($scenario);
        }
    }

    /**
     * Test a specific XML response scenario
     */
    private function testXmlResponseScenario($scenario)
    {
        $statusCode = $scenario['status_code'];
        $contentType = $scenario['content_type'];
        $responseBody = $scenario['response_body'];

        $this->line("    Status Code: {$statusCode}");
        $this->line("    Content Type: " . ($contentType ?: 'empty'));
        $this->line("    Response Length: " . strlen($responseBody));

        // Apply the improved logic
        $isSuccessfulXml = ($statusCode >= 200 && $statusCode < 300) &&
                          (stripos($contentType, 'xml') !== false ||
                           stripos($responseBody, '<?xml') !== false ||
                           stripos($responseBody, '<') !== false);

        $this->line("    Is Successful XML: " . ($isSuccessfulXml ? 'YES' : 'NO'));
        $this->line("    Should Treat as Error: " . (!$isSuccessfulXml ? 'YES' : 'NO'));

        if ($isSuccessfulXml && !empty($responseBody)) {
            try {
                $xml = simplexml_load_string($responseBody);
                if ($xml !== false) {
                    $this->line("    ✅ XML Parsed Successfully");
                    $this->line("    Root Element: " . $xml->getName());
                    if (isset($xml->status)) $this->line("    Status: " . (string)$xml->status);
                    if (isset($xml->message)) $this->line("    Message: " . (string)$xml->message);
                    if (isset($xml->transactionId)) $this->line("    Transaction ID: " . (string)$xml->transactionId);

                    // Check for RAAST-specific elements
                    if (isset($xml->Body->Document->status)) {
                        $this->line("    RAAST Status: " . (string)$xml->Body->Document->status);
                    }
                    if (isset($xml->Body->AppHdr->BizMsgIdr)) {
                        $this->line("    Business Message ID: " . (string)$xml->Body->AppHdr->BizMsgIdr);
                    }
                }
            } catch (\Exception $e) {
                $this->line("    ❌ XML Parse Error: " . $e->getMessage());
            }
        } elseif ($isSuccessfulXml && empty($responseBody)) {
            $this->line("    ✅ Empty successful response (HTTP 200 with no content)");
        }

        // Create the response array as the actual code would
        $responseArray = [
            'error' => !$isSuccessfulXml,
            'message' => $isSuccessfulXml ? 'RAAST API returned XML response' : 'RAAST API returned non-JSON response',
            'status_code' => $statusCode,
            'success' => $isSuccessfulXml
        ];

        $this->line("    Final Result: " . ($responseArray['success'] ? '✅ SUCCESS' : '❌ ERROR'));
    }

    /**
     * Test trace reference consistency across RAAST operations
     */
    private function testTraceReferenceConsistency()
    {
        $this->info("\n🔍 Testing Trace Reference Consistency");
        $this->info('=' . str_repeat('=', 50));

        try {
            // Generate a single trace reference for the entire test
            $testTraceReference = (string) \Illuminate\Support\Str::uuid();
            $this->info("Test Trace Reference: {$testTraceReference}");

            // Test 1: Create a payment with the trace reference
            $controller = new \App\Http\Controllers\Api\RaastController(request());

            $paymentData = [
                'service' => 'N',
                'type' => 'pacs.008.001.08',
                'sender' => 'TEST001',
                'receiver' => 'TEST002',
                'document' => '<Document><Test>Trace Consistency Test</Test></Document>',
                'amount' => 100.00,
                'currency' => 'PKR'
            ];

            // Create a mock request
            $request = new \Illuminate\Http\Request($paymentData);
            $paymentRequest = new \App\Http\Requests\RaastPaymentRequest($paymentData);

            $this->info("\n📝 Step 1: Testing payment with provided trace reference");

            // Call sendPayment with our test trace reference
            $response = $controller->sendPayment($paymentRequest, $testTraceReference);
            $responseData = $response->getData(true);

            $this->info("   Payment Response Success: " . ($responseData['success'] ? '✅' : '❌'));
            $this->info("   Returned Trace Reference: " . ($responseData['trace_reference'] ?? 'N/A'));
            $this->info("   Matches Test Reference: " . (($responseData['trace_reference'] ?? null) === $testTraceReference ? '✅' : '❌'));

            // Test 2: Verify transaction was created with correct trace reference
            $this->info("\n📝 Step 2: Verifying transaction creation");
            $transaction = \App\Models\RaastTransaction::where('trace_reference', $testTraceReference)->first();

            $this->info("   Transaction Found: " . ($transaction ? '✅' : '❌'));
            if ($transaction) {
                $this->info("   Transaction ID: " . $transaction->id);
                $this->info("   Trace Reference Matches: " . ($transaction->trace_reference === $testTraceReference ? '✅' : '❌'));
                $this->info("   Service Type: " . $transaction->service_type);
                $this->info("   Status: " . $transaction->status);
            }

            // Test 3: Test status retrieval with the same trace reference
            $this->info("\n📝 Step 3: Testing status retrieval");
            if ($transaction) {
                $statusResponse = $controller->getTransactionStatus(new \App\Http\Requests\RaastStatusRequest(), $testTraceReference);
                $statusData = $statusResponse->getData(true);

                $this->info("   Status Response Success: " . ($statusData['success'] ? '✅' : '❌'));
                $this->info("   Status Trace Reference: " . ($statusData['data']['trace_reference'] ?? 'N/A'));
                $this->info("   Status Matches Test Reference: " . (($statusData['data']['trace_reference'] ?? null) === $testTraceReference ? '✅' : '❌'));
            }

            $this->info("\n🎉 Trace Reference Consistency Test Completed");
            return 0;

        } catch (\Exception $ex) {
            $this->error("\n❌ Trace Reference Consistency Test Failed");
            $this->error("Error: " . $ex->getMessage());
            $this->error("Trace: " . $ex->getTraceAsString());
            return 1;
        }
    }

    /**
     * Test production RTP-only functionality
     */
    private function testProductionRtpOnlyFunctionality()
    {
        $this->info("\n🚀 Testing Production RTP-Only Functionality");
        $this->info('=' . str_repeat('=', 50));

        try {
            // Test 1: Test RTP-only API endpoint
            $this->info("\n📝 Step 1: Testing RTP-Only API endpoint");

            $controller = new \App\Http\Controllers\Api\RaastController(request());
            $testTraceReference = (string) \Illuminate\Support\Str::uuid();

            $rtpOnlyData = [
                'service' => 'rtp',
                'type' => 'pain.013.001.09',
                'sender' => 'NITBPKKASRTP',
                'receiver' => 'BPUNPKKAXXXX',
                'document' => $this->generateTestRtpXml('RTPONLY' . time()),
                'amount' => 500.00,
                'currency' => 'PKR',
                'payment_purpose' => 'Online Merchant Payment',
                'expiry_minutes' => 30,
                'business_service' => 'RTP'
            ];

            $request = new \App\Http\Requests\RaastPaymentRequest($rtpOnlyData);
            $response = $controller->sendRtpOnly($request, $testTraceReference);
            $responseData = $response->getData(true);

            $this->info("   RTP-Only Response Success: " . ($responseData['success'] ? '✅' : '❌'));
            $this->info("   Trace Reference: " . ($responseData['trace_reference'] ?? 'N/A'));
            $this->info("   Customer Registration Skipped: " . (isset($responseData['data']['customer_registration_skipped']) ? '✅' : '❌'));

            // Test 2: Verify OAuth2 token refresh functionality
            $this->info("\n📝 Step 2: Testing OAuth2 token refresh functionality");

            $tokenManager = app(\App\Services\RaastTokenManager::class);
            $tokenStatus = $tokenManager->getTokenStatus();

            $this->info("   Has Access Token: " . ($tokenStatus['has_access_token'] ? '✅' : '❌'));
            $this->info("   Has Refresh Token: " . ($tokenStatus['has_refresh_token'] ? '✅' : '❌'));
            $this->info("   Token Valid: " . ($tokenStatus['is_valid'] ? '✅' : '❌'));

            if ($tokenStatus['expires_at']) {
                $this->info("   Token Expires At: " . $tokenStatus['expires_at']);
            }

            // Test 3: Verify trace reference consistency
            $this->info("\n📝 Step 3: Verifying trace reference consistency");

            if (isset($responseData['trace_reference'])) {
                $transaction = \App\Models\RaastTransaction::where('trace_reference', $responseData['trace_reference'])->first();

                $this->info("   Transaction Found: " . ($transaction ? '✅' : '❌'));
                if ($transaction) {
                    $this->info("   Service Type: " . $transaction->service_type);
                    $this->info("   Trace Reference Matches: " . ($transaction->trace_reference === $testTraceReference ? '✅' : '❌'));
                }
            }

            // Test 4: Test web form accessibility
            $this->info("\n📝 Step 4: Testing RTP-Only web form");

            try {
                $webResponse = $controller->rtpOnlyForm();
                $this->info("   RTP-Only Form Accessible: ✅");
            } catch (\Exception $ex) {
                $this->error("   RTP-Only Form Error: " . $ex->getMessage());
            }

            $this->info("\n🎉 Production RTP-Only Functionality Test Completed");
            return 0;

        } catch (\Exception $ex) {
            $this->error("\n❌ Production RTP-Only Functionality Test Failed");
            $this->error("Error: " . $ex->getMessage());
            $this->error("Trace: " . $ex->getTraceAsString());
            return 1;
        }
    }

    /**
     * Test production RTP-only method with OAuth2 refresh functionality
     */
    private function testRtpOnlyProductionMethod($verbose = false, $useRealIntegration = false)
    {
        $this->info("\n🚀 Testing Production RTP-Only Method with OAuth2 Refresh");
        $this->info('=' . str_repeat('=', 60));

        try {
            // Test 1: Test RTP-only production method
            $this->info("\n📝 Step 1: Testing Production RTP-Only Method");

            $controller = new \App\Http\Controllers\Api\RaastController(request());
            $testTraceReference = (string) \Illuminate\Support\Str::uuid();

            // Generate test data similar to testRtpOnlyFlow
            $rtpOnlyData = $this->generateRtpOnlyProductionData();

            $this->info("   Generated Test Data:");
            $this->info("   - Trace Reference: {$testTraceReference}");
            $this->info("   - Amount: PKR " . number_format($rtpOnlyData['amount'], 2));
            $this->info("   - Sender: " . $rtpOnlyData['sender']);
            $this->info("   - Receiver: " . $rtpOnlyData['receiver']);
            $this->info("   - Skip Customer Registration: YES");

            // Create request object
            $request = new \App\Http\Requests\RaastPaymentRequest($rtpOnlyData);

            // Call the production RTP-only method
            $response = $controller->sendRtpOnly($request, $testTraceReference);
            $responseData = $response->getData(true);

            $this->info("\n   Production RTP-Only Results:");
            $this->info("   - Success: " . ($responseData['success'] ? '✅' : '❌'));
            $this->info("   - Trace Reference: " . ($responseData['trace_reference'] ?? 'N/A'));
            $this->info("   - Transaction ID: " . ($responseData['transaction_id'] ?? 'N/A'));

            if (isset($responseData['data']['rtp_only'])) {
                $this->info("   - RTP-Only Flag: " . ($responseData['data']['rtp_only'] ? '✅' : '❌'));
            }

            if (isset($responseData['data']['customer_registration_skipped'])) {
                $this->info("   - Customer Registration Skipped: " . ($responseData['data']['customer_registration_skipped'] ? '✅' : '❌'));
            }

            // Test 2: Verify OAuth2 token refresh functionality
            $this->info("\n📝 Step 2: Testing OAuth2 Token Refresh Functionality");

            $tokenManager = app(\App\Services\RaastTokenManager::class);

            // Get current token status
            $this->info("   Checking current OAuth2 token status...");

            try {
                $accessToken = $tokenManager->getValidAccessToken();
                $this->info("   - Access Token Available: " . ($accessToken ? '✅' : '❌'));

                if ($accessToken) {
                    $this->info("   - Token Length: " . strlen($accessToken) . " characters");
                    $this->info("   - Token Prefix: " . substr($accessToken, 0, 20) . "...");
                }

                // Test token refresh capability
                $this->info("   Testing token refresh capability...");
                $refreshResult = $tokenManager->refreshAccessToken();
                $this->info("   - Token Refresh Test: " . ($refreshResult ? '✅' : '❌'));

            } catch (\Exception $ex) {
                $this->error("   OAuth2 Token Error: " . $ex->getMessage());
            }

            // Test 3: Verify trace reference consistency
            $this->info("\n📝 Step 3: Verifying Trace Reference Consistency");

            if (isset($responseData['trace_reference'])) {
                $transaction = \App\Models\RaastTransaction::where('trace_reference', $responseData['trace_reference'])->first();

                $this->info("   - Transaction Found: " . ($transaction ? '✅' : '❌'));
                if ($transaction) {
                    $this->info("   - Service Type: " . $transaction->service_type);
                    $this->info("   - Status: " . $transaction->status);
                    $this->info("   - Trace Reference Matches: " . ($transaction->trace_reference === $testTraceReference ? '✅' : '❌'));
                    $this->info("   - Created At: " . $transaction->created_at);
                }
            }

            // Test 4: Test error handling and retry logic
            $this->info("\n📝 Step 4: Testing Error Handling and Retry Logic");

            if ($useRealIntegration) {
                $this->info("   Testing with real RAAST integration...");

                // Test with invalid data to trigger error handling
                $invalidData = $rtpOnlyData;
                $invalidData['sender'] = 'INVALID001'; // Invalid sender

                $invalidRequest = new \App\Http\Requests\RaastPaymentRequest($invalidData);
                $invalidResponse = $controller->sendRtpOnly($invalidRequest);
                $invalidResponseData = $invalidResponse->getData(true);

                $this->info("   - Error Handling Test: " . (!$invalidResponseData['success'] ? '✅' : '❌'));
                if (!$invalidResponseData['success']) {
                    $this->info("   - Error Message: " . ($invalidResponseData['message'] ?? 'N/A'));
                }
            } else {
                $this->info("   Skipping real integration tests (use --real-integration flag)");
            }

            // Test 5: Performance and timing
            $this->info("\n📝 Step 5: Performance Analysis");

            $startTime = microtime(true);

            // Make another RTP-only call to measure performance
            $perfTestData = $this->generateRtpOnlyProductionData();
            $perfRequest = new \App\Http\Requests\RaastPaymentRequest($perfTestData);
            $perfResponse = $controller->sendRtpOnly($perfRequest);

            $endTime = microtime(true);
            $executionTime = round(($endTime - $startTime) * 1000, 2);

            $this->info("   - Execution Time: {$executionTime}ms");
            $this->info("   - Performance: " . ($executionTime < 5000 ? '✅ Fast' : '⚠️ Slow'));

            $this->info("\n🎉 Production RTP-Only Method Test Completed Successfully");
            return 0;

        } catch (\Exception $ex) {
            $this->error("\n❌ Production RTP-Only Method Test Failed");
            $this->error("Error: " . $ex->getMessage());
            if ($verbose) {
                $this->error("Trace: " . $ex->getTraceAsString());
            }
            return 1;
        }
    }

    /**
     * Generate RTP-only production test data
     */
    private function generateRtpOnlyProductionData()
    {
        $messageId = 'RTPONLY' . time() . rand(100, 999);

        return [
            'service' => 'rtp',
            'type' => 'pain.013.001.09',
            'sender' => 'NITBPKKASRTP',
            'receiver' => 'BPUNPKKAXXXX',
            'document' => $this->generateTestRtpXml($messageId, true),
            'amount' => 750.00,
            'currency' => 'PKR',
            'payment_purpose' => 'Online Merchant Payment - RTP Only',
            'expiry_minutes' => 30,
            'business_service' => 'RTP',
            'message_id' => $messageId
        ];
    }

    /**
     * Parse RAAST XML response and extract meaningful data
     * Handles various RAAST XML schemas like pacs.002.001.10, pain.014.001.07, etc.
     */
    private function parseRaastXmlResponse($xmlContent, $verbose = false)
    {
        $result = [
            'xml_parsed' => false,
            'xml_parse_error' => null,
            'xml_root_element' => null,
            'raast_message_type' => null,
            'transaction_status' => null,
            'status_code' => null,
            'error_code' => null,
            'error_description' => null,
            'payment_reference' => null,
            'original_message_id' => null,
            'timestamp' => null,
            'success' => false,
            'xml_content_preview' => substr($xmlContent, 0, 500) // First 500 chars for debugging
        ];

        try {
            // Clean and prepare XML content
            $cleanXml = $this->cleanXmlContent($xmlContent);

            // Parse XML
            libxml_use_internal_errors(true);
            $xml = simplexml_load_string($cleanXml);

            if ($xml === false) {
                $xmlErrors = libxml_get_errors();
                $result['xml_parse_error'] = 'XML parsing failed: ' . implode('; ', array_map(function($error) {
                    return $error->message;
                }, $xmlErrors));
                libxml_clear_errors();
                return $result;
            }

            $result['xml_parsed'] = true;
            $result['xml_root_element'] = $xml->getName();

            // Register namespaces for RAAST XML schemas
            $namespaces = $xml->getNamespaces(true);
            foreach ($namespaces as $prefix => $namespace) {
                $xml->registerXPathNamespace($prefix ?: 'default', $namespace);
            }

            if ($verbose) {
                $this->info("📋 XML Parsing Details:");
                $this->info("   Root Element: " . $result['xml_root_element']);
                $this->info("   Namespaces: " . json_encode($namespaces));
            }

            // Detect RAAST message type and parse accordingly
            $messageType = $this->detectRaastMessageType($xml, $namespaces);
            $result['raast_message_type'] = $messageType;

            switch ($messageType) {
                case 'pacs.002.001.10': // Payment Status Report
                    $result = array_merge($result, $this->parsePacs002Response($xml, $verbose));
                    break;

                case 'pain.014.001.07': // RTP Status Response
                    $result = array_merge($result, $this->parsePain014Response($xml, $verbose));
                    break;

                case 'pain.013.001.09': // RTP Request Response
                    $result = array_merge($result, $this->parsePain013Response($xml, $verbose));
                    break;

                case 'camt.055.001.08': // Cancellation Response
                    $result = array_merge($result, $this->parseCamt055Response($xml, $verbose));
                    break;

                default:
                    // Generic XML parsing for unknown schemas
                    $result = array_merge($result, $this->parseGenericRaastXml($xml, $verbose));
                    break;
            }

            // Determine overall success based on parsed data
            $result['success'] = $this->determineXmlResponseSuccess($result);

            if ($verbose) {
                $this->info("✅ XML Response Parsed Successfully:");
                $this->info("   Message Type: " . ($result['raast_message_type'] ?? 'Unknown'));
                $this->info("   Transaction Status: " . ($result['transaction_status'] ?? 'Unknown'));
                $this->info("   Success: " . ($result['success'] ? 'YES' : 'NO'));
                if ($result['error_code']) {
                    $this->info("   Error Code: " . $result['error_code']);
                    $this->info("   Error Description: " . $result['error_description']);
                }
            }

        } catch (\Exception $e) {
            $result['xml_parse_error'] = 'Exception during XML parsing: ' . $e->getMessage();
            if ($verbose) {
                $this->error("❌ XML Parsing Exception: " . $e->getMessage());
            }
        }

        return $result;
    }

    /**
     * Clean XML content for parsing
     */
    private function cleanXmlContent($xmlContent)
    {
        // Remove BOM if present
        $xmlContent = preg_replace('/^\xEF\xBB\xBF/', '', $xmlContent);

        // Fix encoding declaration if needed
        $xmlContent = preg_replace('/(<\?xml[^?]+?)utf-16/i', '$1utf-8', $xmlContent);

        // Remove any leading/trailing whitespace
        return trim($xmlContent);
    }

    /**
     * Detect RAAST message type from XML structure
     */
    private function detectRaastMessageType($xml, $namespaces)
    {
        $rootElement = $xml->getName();

        // Check for common RAAST message types based on root element and structure
        if ($rootElement === 'Document') {
            // Look for specific child elements that indicate message type
            $children = $xml->children();
            foreach ($children as $child) {
                $childName = $child->getName();

                // Map child elements to message types
                switch ($childName) {
                    case 'FIToFIPmtStsRpt':
                        return 'pacs.002.001.10'; // Payment Status Report
                    case 'CdtrPmtActvtnReq':
                        return 'pain.013.001.09'; // RTP Request
                    case 'CdtrPmtActvtnStsRpt':
                        return 'pain.014.001.07'; // RTP Status Report
                    case 'FIToFIPmtCxlReq':
                        return 'camt.055.001.08'; // Cancellation Request
                }
            }
        }

        // Check for DataPDU structure (alternative RAAST format)
        if ($rootElement === 'DataPDU') {
            return 'raast.datapdu';
        }

        // Check for error responses
        if (stripos($xml->asXML(), 'error') !== false || stripos($xml->asXML(), 'fault') !== false) {
            return 'error.response';
        }

        return 'unknown';
    }

    /**
     * Parse pacs.002.001.10 (Payment Status Report) response
     */
    private function parsePacs002Response($xml, $verbose = false)
    {
        $result = [];

        try {
            // Navigate to FIToFIPmtStsRpt
            $statusReport = $xml->FIToFIPmtStsRpt ?? $xml->children()->FIToFIPmtStsRpt;

            if ($statusReport) {
                // Extract Group Header information
                if ($statusReport->GrpHdr) {
                    $result['original_message_id'] = (string)$statusReport->GrpHdr->MsgId;
                    $result['timestamp'] = (string)$statusReport->GrpHdr->CreDtTm;
                }

                // Extract Transaction Information and Status
                if ($statusReport->TxInfAndSts) {
                    $txInfo = $statusReport->TxInfAndSts;

                    // Transaction status (ACCP, RJCT, PDNG, etc.)
                    $result['transaction_status'] = (string)$txInfo->TxSts;

                    // Status reason code
                    if ($txInfo->StsRsnInf) {
                        $result['status_code'] = (string)$txInfo->StsRsnInf->Rsn->Cd;
                        $result['error_code'] = $result['status_code'];
                        $result['error_description'] = (string)$txInfo->StsRsnInf->AddtlInf;
                    }

                    // Original transaction reference
                    $result['payment_reference'] = (string)$txInfo->OrgnlTxRef->TxId;
                }

                if ($verbose) {
                    $this->info("📊 pacs.002 Payment Status Report:");
                    $this->info("   Status: " . ($result['transaction_status'] ?? 'Unknown'));
                    $this->info("   Reference: " . ($result['payment_reference'] ?? 'Unknown'));
                }
            }
        } catch (\Exception $e) {
            $result['parse_error'] = 'pacs.002 parsing error: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Parse pain.014.001.07 (RTP Status Report) response
     */
    private function parsePain014Response($xml, $verbose = false)
    {
        $result = [];

        try {
            // Navigate to CdtrPmtActvtnStsRpt
            $statusReport = $xml->CdtrPmtActvtnStsRpt ?? $xml->children()->CdtrPmtActvtnStsRpt;

            if ($statusReport) {
                // Extract Group Header
                if ($statusReport->GrpHdr) {
                    $result['original_message_id'] = (string)$statusReport->GrpHdr->MsgId;
                    $result['timestamp'] = (string)$statusReport->GrpHdr->CreDtTm;
                }

                // Extract Payment Information and Status
                if ($statusReport->PmtInfAndSts) {
                    $pmtInfo = $statusReport->PmtInfAndSts;

                    // RTP status (ACCP, RJCT, PDNG, etc.)
                    $result['transaction_status'] = (string)$pmtInfo->PmtInfSts;

                    // Status reason
                    if ($pmtInfo->StsRsnInf) {
                        $result['status_code'] = (string)$pmtInfo->StsRsnInf->Rsn->Cd;
                        $result['error_code'] = $result['status_code'];
                        $result['error_description'] = (string)$pmtInfo->StsRsnInf->AddtlInf;
                    }

                    // Original payment information ID
                    $result['payment_reference'] = (string)$pmtInfo->OrgnlPmtInfId;
                }

                if ($verbose) {
                    $this->info("📊 pain.014 RTP Status Report:");
                    $this->info("   Status: " . ($result['transaction_status'] ?? 'Unknown'));
                    $this->info("   Reference: " . ($result['payment_reference'] ?? 'Unknown'));
                }
            }
        } catch (\Exception $e) {
            $result['parse_error'] = 'pain.014 parsing error: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Parse pain.013.001.09 (RTP Request) response
     */
    private function parsePain013Response($xml, $verbose = false)
    {
        $result = [];

        try {
            // Navigate to CdtrPmtActvtnReq
            $rtpRequest = $xml->CdtrPmtActvtnReq ?? $xml->children()->CdtrPmtActvtnReq;

            if ($rtpRequest) {
                // Extract Group Header
                if ($rtpRequest->GrpHdr) {
                    $result['original_message_id'] = (string)$rtpRequest->GrpHdr->MsgId;
                    $result['timestamp'] = (string)$rtpRequest->GrpHdr->CreDtTm;
                }

                // For RTP requests, success is typically indicated by proper structure
                $result['transaction_status'] = 'ACCP'; // Accepted
                $result['payment_reference'] = $result['original_message_id'];

                if ($verbose) {
                    $this->info("📊 pain.013 RTP Request Response:");
                    $this->info("   Message ID: " . ($result['original_message_id'] ?? 'Unknown'));
                    $this->info("   Status: RTP Created Successfully");
                }
            }
        } catch (\Exception $e) {
            $result['parse_error'] = 'pain.013 parsing error: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Parse camt.055.001.08 (Cancellation) response
     */
    private function parseCamt055Response($xml, $verbose = false)
    {
        $result = [];

        try {
            // Navigate to FIToFIPmtCxlReq
            $cancelRequest = $xml->FIToFIPmtCxlReq ?? $xml->children()->FIToFIPmtCxlReq;

            if ($cancelRequest) {
                // Extract Group Header
                if ($cancelRequest->GrpHdr) {
                    $result['original_message_id'] = (string)$cancelRequest->GrpHdr->MsgId;
                    $result['timestamp'] = (string)$cancelRequest->GrpHdr->CreDtTm;
                }

                // Extract Transaction Information
                if ($cancelRequest->TxInf) {
                    $result['payment_reference'] = (string)$cancelRequest->TxInf->CxlId;
                    $result['transaction_status'] = 'CANC'; // Cancelled
                }

                if ($verbose) {
                    $this->info("📊 camt.055 Cancellation Response:");
                    $this->info("   Cancellation ID: " . ($result['payment_reference'] ?? 'Unknown'));
                    $this->info("   Status: Cancellation Processed");
                }
            }
        } catch (\Exception $e) {
            $result['parse_error'] = 'camt.055 parsing error: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Parse generic RAAST XML when specific schema is not recognized
     */
    private function parseGenericRaastXml($xml, $verbose = false)
    {
        $result = [];

        try {
            // Look for common status indicators
            $xmlString = $xml->asXML();

            // Check for common status elements
            if (preg_match('/<(?:status|Status|TxSts|PmtInfSts)>([^<]+)<\/(?:status|Status|TxSts|PmtInfSts)>/i', $xmlString, $matches)) {
                $result['transaction_status'] = trim($matches[1]);
            }

            // Check for error codes
            if (preg_match('/<(?:code|Code|Cd)>([^<]+)<\/(?:code|Code|Cd)>/i', $xmlString, $matches)) {
                $result['error_code'] = trim($matches[1]);
            }

            // Check for messages
            if (preg_match('/<(?:message|Message|AddtlInf)>([^<]+)<\/(?:message|Message|AddtlInf)>/i', $xmlString, $matches)) {
                $result['error_description'] = trim($matches[1]);
            }

            // Check for message IDs
            if (preg_match('/<(?:MsgId|MessageId|messageId)>([^<]+)<\/(?:MsgId|MessageId|messageId)>/i', $xmlString, $matches)) {
                $result['original_message_id'] = trim($matches[1]);
            }

            // Check for timestamps
            if (preg_match('/<(?:CreDtTm|timestamp|Timestamp)>([^<]+)<\/(?:CreDtTm|timestamp|Timestamp)>/i', $xmlString, $matches)) {
                $result['timestamp'] = trim($matches[1]);
            }

            if ($verbose) {
                $this->info("📊 Generic XML Response:");
                $this->info("   Status: " . ($result['transaction_status'] ?? 'Unknown'));
                $this->info("   Message ID: " . ($result['original_message_id'] ?? 'Unknown'));
            }
        } catch (\Exception $e) {
            $result['parse_error'] = 'Generic XML parsing error: ' . $e->getMessage();
        }

        return $result;
    }

    /**
     * Determine if XML response indicates success
     */
    private function determineXmlResponseSuccess($parsedResult)
    {
        // Handle empty successful responses (HTTP 200 with no content)
        if (isset($parsedResult['response_type']) && $parsedResult['response_type'] === 'empty_success') {
            return true;
        }

        // If there was a parsing error, it's not successful
        if (!empty($parsedResult['xml_parse_error']) || !$parsedResult['xml_parsed']) {
            return false;
        }

        // Check transaction status for success indicators
        $status = strtoupper($parsedResult['transaction_status'] ?? '');

        // RAAST success status codes
        $successStatuses = ['ACCP', 'ACTC', 'ACWC', 'ACSC', 'ACWP', 'ACCC'];

        if (in_array($status, $successStatuses)) {
            return true;
        }

        // RAAST failure status codes
        $failureStatuses = ['RJCT', 'CANC', 'PDNG'];

        if (in_array($status, $failureStatuses)) {
            return false;
        }

        // If no explicit error code and XML was parsed successfully, consider it success
        if (empty($parsedResult['error_code']) && empty($parsedResult['parse_error'])) {
            return true;
        }

        return false;
    }

    /**
     * Format XML status message for display
     */
    private function formatXmlStatusMessage($xmlResult)
    {
        $parts = [];

        // Handle empty successful responses
        if (isset($xmlResult['response_type']) && $xmlResult['response_type'] === 'empty_success') {
            return 'Empty Success Response (HTTP 200)';
        }

        if (isset($xmlResult['raast_message_type'])) {
            $parts[] = $xmlResult['raast_message_type'];
        }

        if (isset($xmlResult['transaction_status'])) {
            $parts[] = 'Status: ' . $xmlResult['transaction_status'];
        }

        if (isset($xmlResult['error_code'])) {
            $parts[] = 'Error: ' . $xmlResult['error_code'];
        }

        if (isset($xmlResult['success_reason'])) {
            $parts[] = $xmlResult['success_reason'];
        }

        if (empty($parts)) {
            return $xmlResult['success'] ? 'Response (Success)' : 'Response (Failed)';
        }

        return implode(' | ', $parts);
    }
}
