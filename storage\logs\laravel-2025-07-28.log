[2025-07-28 15:22:04] local.ERROR: There are no commands defined in the "generate" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"generate\" namespace. at C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php:632)
[stacktrace]
#0 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(683): Symfony\\Component\\Console\\Application->findNamespace('generate')
#1 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(256): Symfony\\Component\\Console\\Application->find('generate:access...')
#2 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(168): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\wamp64\\www\\ict\\city-islamabad-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-07-28 15:22:44] local.ERROR: cURL error 28: Failed to connect to 192.168.250.21 port 23432 after 10016 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://192.168.250.21:23432/token {"exception":"[object] (Illuminate\\Http\\Client\\ConnectionException(code: 0): cURL error 28: Failed to connect to 192.168.250.21 port 23432 after 10016 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://192.168.250.21:23432/token at C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php:855)
[stacktrace]
#0 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(247): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#1 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(821): retry(0, Object(Closure), 100, Object(Closure))
#2 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(727): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'https://192.168...', Array)
#3 C:\\wamp64\\www\\ict\\city-islamabad-app\\app\\Console\\Commands\\GenerateAccessToken.php(49): Illuminate\\Http\\Client\\PendingRequest->post('https://192.168...', Array)
#4 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\GenerateAccessToken->handle()
#5 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#6 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#7 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#8 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#9 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(194): Illuminate\\Container\\Container->call(Array)
#10 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Command\\Command.php(312): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#11 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(163): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#12 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(1022): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#13 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(314): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\GenerateAccessToken), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#14 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(168): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#15 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#16 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#17 C:\\wamp64\\www\\ict\\city-islamabad-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#18 {main}

[previous exception] [object] (GuzzleHttp\\Exception\\ConnectException(code: 0): cURL error 28: Failed to connect to 192.168.250.21 port 23432 after 10016 ms: Timeout was reached (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://192.168.250.21:23432/token at C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php:210)
[stacktrace]
#0 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(158): GuzzleHttp\\Handler\\CurlFactory::createRejection(Object(GuzzleHttp\\Handler\\EasyHandle), Array)
#1 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlFactory.php(110): GuzzleHttp\\Handler\\CurlFactory::finishError(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#2 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\CurlHandler.php(47): GuzzleHttp\\Handler\\CurlFactory::finish(Object(GuzzleHttp\\Handler\\CurlHandler), Object(GuzzleHttp\\Handler\\EasyHandle), Object(GuzzleHttp\\Handler\\CurlFactory))
#3 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(28): GuzzleHttp\\Handler\\CurlHandler->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#4 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Handler\\Proxy.php(48): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#5 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1150): GuzzleHttp\\Handler\\Proxy::GuzzleHttp\\Handler\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#6 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1116): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#7 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(1102): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#8 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\PrepareBodyMiddleware.php(64): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#9 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(37): GuzzleHttp\\PrepareBodyMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#10 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\RedirectMiddleware.php(71): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#11 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Middleware.php(61): GuzzleHttp\\RedirectMiddleware->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#12 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\HandlerStack.php(75): GuzzleHttp\\Middleware::GuzzleHttp\\{closure}(Object(GuzzleHttp\\Psr7\\Request), Array)
#13 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(331): GuzzleHttp\\HandlerStack->__invoke(Object(GuzzleHttp\\Psr7\\Request), Array)
#14 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(168): GuzzleHttp\\Client->transfer(Object(GuzzleHttp\\Psr7\\Request), Array)
#15 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\guzzlehttp\\guzzle\\src\\Client.php(187): GuzzleHttp\\Client->requestAsync('POST', Object(GuzzleHttp\\Psr7\\Uri), Array)
#16 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(961): GuzzleHttp\\Client->request('POST', 'https://192.168...', Array)
#17 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(823): Illuminate\\Http\\Client\\PendingRequest->sendRequest('POST', 'https://192.168...', Array)
#18 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(247): Illuminate\\Http\\Client\\PendingRequest->Illuminate\\Http\\Client\\{closure}(1)
#19 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(821): retry(0, Object(Closure), 100, Object(Closure))
#20 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Client\\PendingRequest.php(727): Illuminate\\Http\\Client\\PendingRequest->send('POST', 'https://192.168...', Array)
#21 C:\\wamp64\\www\\ict\\city-islamabad-app\\app\\Console\\Commands\\GenerateAccessToken.php(49): Illuminate\\Http\\Client\\PendingRequest->post('https://192.168...', Array)
#22 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(36): App\\Console\\Commands\\GenerateAccessToken->handle()
#23 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#24 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#25 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\BoundMethod.php(35): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Illuminate\\Foundation\\Application), Array, Object(Closure))
#26 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(661): Illuminate\\Container\\BoundMethod::call(Object(Illuminate\\Foundation\\Application), Array, Array, NULL)
#27 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(194): Illuminate\\Container\\Container->call(Array)
#28 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Command\\Command.php(312): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#29 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Command.php(163): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#30 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(1022): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#31 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(314): Symfony\\Component\\Console\\Application->doRunCommand(Object(App\\Console\\Commands\\GenerateAccessToken), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#32 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(168): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#33 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#34 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#35 C:\\wamp64\\www\\ict\\city-islamabad-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#36 {main}
"} 
[2025-07-28 16:48:01] local.ERROR: There are no commands defined in the "test" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"test\" namespace. at C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php:632)
[stacktrace]
#0 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(683): Symfony\\Component\\Console\\Application->findNamespace('test')
#1 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(256): Symfony\\Component\\Console\\Application->find('test:raast-end-...')
#2 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\symfony\\console\\Application.php(168): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Console\\Application.php(102): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\wamp64\\www\\ict\\city-islamabad-app\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(155): Illuminate\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\wamp64\\www\\ict\\city-islamabad-app\\artisan(35): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#6 {main}
"} 
[2025-07-28 16:48:23] local.ERROR: XML Digital Signature Error: Array to string conversion  
[2025-07-28 16:48:23] local.WARNING: Failed to add digital signature to RTP XML: Failed to sign XML document: Array to string conversion  
[2025-07-28 16:48:23] local.WARNING: Exception details: #0 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(1342): App\Services\XmlDigitalSignatureService->signXmlDocument('<DataPDU xmlns=...', false)
#1 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(528): App\Console\Commands\TestRaastEndToEnd->generateTestRtpXml('RTPONLY17537033...')
#2 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(206): App\Console\Commands\TestRaastEndToEnd->generateRtpOnlyTestData()
#3 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(169): App\Console\Commands\TestRaastEndToEnd->testRtpOnlyFlow(1, false, false)
#4 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Console\Commands\TestRaastEndToEnd->handle()
#5 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#6 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#7 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\Container.php(661): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Command.php(194): Illuminate\Container\Container->call(Array)
#10 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Command\Command.php(312): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#11 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Command.php(163): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#12 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(1022): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(314): Symfony\Component\Console\Application->doRunCommand(Object(App\Console\Commands\TestRaastEndToEnd), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#14 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(168): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#15 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Application.php(102): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#16 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(155): Illuminate\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#17 C:\wamp64\www\ict\city-islamabad-app\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#18 {main}  
[2025-07-28 16:48:23] local.INFO: CAS JWT token generated with x5t {"x5t":"odnPd4xrPMTcVD0bLNPghzQ9szQ","expires_in":"5"} 
[2025-07-28 16:48:33] local.ERROR: XML Digital Signature Error: Array to string conversion  
[2025-07-28 16:48:33] local.WARNING: Failed to add digital signature to RTP XML: Failed to sign XML document: Array to string conversion  
[2025-07-28 16:48:33] local.WARNING: Exception details: #0 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(1342): App\Services\XmlDigitalSignatureService->signXmlDocument('<DataPDU xmlns=...', false)
#1 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(528): App\Console\Commands\TestRaastEndToEnd->generateTestRtpXml('RTPONLY17537033...')
#2 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(206): App\Console\Commands\TestRaastEndToEnd->generateRtpOnlyTestData()
#3 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(169): App\Console\Commands\TestRaastEndToEnd->testRtpOnlyFlow(1, true, false)
#4 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Console\Commands\TestRaastEndToEnd->handle()
#5 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#6 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#7 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\Container.php(661): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Command.php(194): Illuminate\Container\Container->call(Array)
#10 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Command\Command.php(312): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#11 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Command.php(163): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#12 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(1022): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(314): Symfony\Component\Console\Application->doRunCommand(Object(App\Console\Commands\TestRaastEndToEnd), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#14 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(168): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#15 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Application.php(102): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#16 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(155): Illuminate\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#17 C:\wamp64\www\ict\city-islamabad-app\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#18 {main}  
[2025-07-28 17:03:44] local.ERROR: XML Digital Signature Error Occurred: Array to string conversion  
[2025-07-28 17:03:44] local.WARNING: Failed to add digital signature to RTP XML: Failed to sign XML document: Array to string conversion  
[2025-07-28 17:03:44] local.WARNING: Exception details: #0 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(1342): App\Services\XmlDigitalSignatureService->signXmlDocument('<DataPDU xmlns=...', false)
#1 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(528): App\Console\Commands\TestRaastEndToEnd->generateTestRtpXml('RTPONLY17537042...')
#2 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(206): App\Console\Commands\TestRaastEndToEnd->generateRtpOnlyTestData()
#3 C:\wamp64\www\ict\city-islamabad-app\app\Console\Commands\TestRaastEndToEnd.php(169): App\Console\Commands\TestRaastEndToEnd->testRtpOnlyFlow(1, true, false)
#4 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(36): App\Console\Commands\TestRaastEndToEnd->handle()
#5 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\Util.php(41): Illuminate\Container\BoundMethod::Illuminate\Container\{closure}()
#6 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(93): Illuminate\Container\Util::unwrapIfClosure(Object(Closure))
#7 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\BoundMethod.php(35): Illuminate\Container\BoundMethod::callBoundMethod(Object(Illuminate\Foundation\Application), Array, Object(Closure))
#8 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Container\Container.php(661): Illuminate\Container\BoundMethod::call(Object(Illuminate\Foundation\Application), Array, Array, NULL)
#9 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Command.php(194): Illuminate\Container\Container->call(Array)
#10 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Command\Command.php(312): Illuminate\Console\Command->execute(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#11 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Command.php(163): Symfony\Component\Console\Command\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Illuminate\Console\OutputStyle))
#12 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(1022): Illuminate\Console\Command->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#13 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(314): Symfony\Component\Console\Application->doRunCommand(Object(App\Console\Commands\TestRaastEndToEnd), Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#14 C:\wamp64\www\ict\city-islamabad-app\vendor\symfony\console\Application.php(168): Symfony\Component\Console\Application->doRun(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#15 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Console\Application.php(102): Symfony\Component\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#16 C:\wamp64\www\ict\city-islamabad-app\vendor\laravel\framework\src\Illuminate\Foundation\Console\Kernel.php(155): Illuminate\Console\Application->run(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#17 C:\wamp64\www\ict\city-islamabad-app\artisan(35): Illuminate\Foundation\Console\Kernel->handle(Object(Symfony\Component\Console\Input\ArgvInput), Object(Symfony\Component\Console\Output\ConsoleOutput))
#18 {main}  
[2025-07-28 17:03:44] local.INFO: CAS JWT token generated with x5t {"x5t":"odnPd4xrPMTcVD0bLNPghzQ9szQ","expires_in":"5"} 
[2025-07-28 18:12:03] local.INFO: CAS JWT token generated with x5t {"x5t":"odnPd4xrPMTcVD0bLNPghzQ9szQ","expires_in":"5"} 
[2025-07-28 18:23:23] local.INFO: CAS JWT token generated with x5t {"x5t":"odnPd4xrPMTcVD0bLNPghzQ9szQ","expires_in":"5"} 
[2025-07-28 18:36:43] local.INFO: CAS JWT token generated with x5t {"x5t":"odnPd4xrPMTcVD0bLNPghzQ9szQ","expires_in":"5"} 
