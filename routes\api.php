<?php

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

use App\Http\Controllers\CasWebhookController;
use App\Models\ArmDealer;
use App\Models\CustomTypeAppointment;
use App\Models\Gender;
use App\Models\Location;
use App\Models\MembershipType;
use App\Models\Nationality;
use App\Models\Occupation;
use App\Models\User;
use Illuminate\Support\Str;
use App\Http\Controllers\Api\PAK\OneInformationController;
use App\Http\Controllers\Api\V2\DropdownController;


Route::group(['middleware' => 'location'], function () {

    // PITB Apis

    Route::post('domicile/post', 'Admin\DomicileController@postDomicile');

    // Taxi Information APIs
    Route::get('taxi-info/{taxi_id}', 'Api\TaxiController@getTaxiInfo')->where('taxi_id', '[0-9]+');
    Route::middleware(['throttle:60,1'])->group(function () {
        Route::get('taxi/{taxi_id}', 'Api\TaxiController@getTaxiInfo')->where('taxi_id', '[0-9]+');
    });

    // Taxi Information Routes
    Route::prefix('taxi')->group(function () {
        Route::get('/', 'Api\TaxiController@qrScanner')->name('taxi.scanner');
        Route::get('qr-scanner', 'Api\TaxiController@qrScanner')->name('taxi.qr-scanner');
        Route::get('{taxi_id}', 'Api\TaxiController@showTaxiInfo')->where('taxi_id', '[0-9]+')->name('taxi.info');
    });

    // RAAST Payment APIs
    Route::prefix('raast')->middleware(['throttle:30,1'])->group(function () {
        Route::post('payment', 'Api\RaastController@sendPayment')->name('raast.payment');
        Route::post('rtp', 'Api\RaastController@sendRtp')->name('raast.rtp');
        Route::post('rtp-only', 'Api\RaastController@sendRtpOnly')->name('raast.rtp-only');
        Route::get('status/{trace_reference}', 'Api\RaastController@getTransactionStatus')
            ->where('trace_reference', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')
            ->name('raast.status');
        Route::delete('rtp/{trace_reference}', 'Api\RaastController@cancelRtp')
            ->where('trace_reference', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')
            ->name('raast.cancel');
        Route::get('test/trace-consistency', 'Api\RaastController@testTraceReferenceConsistency')
            ->name('raast.test.trace-consistency');

        // Customer Management APIs
        Route::get('customer-info', 'Api\RaastController@getCustomerInfo')->name('raast.customer.info');
        Route::post('customer-account', 'Api\RaastController@registerCustomerAccount')->name('raast.customer.account');

        // Document Verification APIs
        Route::post('verify-signature', 'Api\RaastController@verifyDocumentSignature')->name('raast.verify.signature');

        // Cache Management APIs
        Route::post('clear-caches', 'Api\RaastController@clearServiceCaches')->name('raast.clear.caches');
    });

    // CAS Webhook APIs
    Route::prefix('cas')->middleware(['throttle:60,1'])->group(function () {
        Route::post('webhook', 'CasWebhookController@handleWebhook')->name('cas.webhook');
        Route::get('status/{message_id}', 'CasWebhookController@getMessageStatus')
            ->where('message_id', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')
            ->name('cas.status');
        Route::get('messages', 'CasWebhookController@listMessages')->name('cas.messages');
        Route::post('messages/{message_id}/reprocess', 'CasWebhookController@reprocessMessage')
            ->where('message_id', '[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}')
            ->name('cas.reprocess');
        Route::get('health', function () {
            $integrationService = app(\App\Services\CasIntegrationService::class);
            return response()->json($integrationService->getHealthStatus());
        })->name('cas.health');
    });

    Route::get('test-form', function () {
        return view('test_form');
    });

    Route::get('explore', function () {
        return view('cityguide.generic');
    });

    Route::get('excise-form', 'Api\TokenTaxAssessmentController@getExciseForm');
    Route::post('excise-generate-psid', 'Api\TokenTaxAssessmentController@postGeneratePsid');
    Route::post('generate-psid', 'Api\TokenTaxAssessmentController@generatePsid');

    Route::post('test-form-upload', function () {
        dd('Form uploaded');
    });
    //Arms Dealers Mobile Web Views
    Route::get('arm-dealers', function () {

        $user = auth('api')->user();
        //  $user = User::find(173931);
        if (!empty($user)) {
            $armDealer = ArmDealer::where('user_id', $user->id)->where('deleted_at', null)->first();
            if (!empty($armDealer)) {
                $user_id = $user->id;
                $armDealerId = $armDealer->id;
            } else {
                abort('403');
            }
            return view('arm_dealers.web_views.categories', compact('user_id', 'armDealerId'));
        } else {
            abort('403');
        }
    });
    Route::get('/webview/arm-dealer/ammunation/{type}/{user_id}/{armDealerId}', function ($type, $user_id, $armDealerId) {
        $product_type = 'ammunation';
        $calibers = \App\Models\ArmCaliber::get();
        return view('arm_dealers.web_views.amm_reg_form', compact('type', 'user_id', 'product_type', 'armDealerId', 'calibers'));
    });
    Route::get('/webview/arm-dealer/arm/{type}/{user_id}/{armDealerId}', function ($type, $user_id, $armDealerId) {
        $product_type = 'arm';
        $calibers = \App\Models\ArmCaliber::get();
        return view('arm_dealers.web_views.arm_reg_form', compact('type', 'user_id', 'product_type', 'armDealerId', 'calibers'));
    });
    Route::get('webview/sports-form', function () {
        $occupations = Occupation::all();
        $genders = Gender::all();
        $nationalities = Nationality::all();
        $membershipTypes = MembershipType::all();
        $membershipCategories = App\Models\MembershipCategory::all();

        return view('sports.membership_form', compact('occupations', 'genders', 'nationalities', 'membershipTypes', 'membershipCategories'));
    });
    Route::post('/webview/sports-form/storeWebViewForm', 'Admin\SportsController@storeWebViewForm')->name('sports.webview.store');
    Route::get('/webview/arm-dealers', 'Admin\ArmDealersController@armDealersListing');

    Route::post('/webview/arm-dealers/inventory/update', 'Admin\ArmDearlerInventoriesController@store');
    Route::get('/webview/arm-dealers/arms/listing', 'Admin\ArmDearlerInventoriesController@showArmsListing');
    Route::get('/webview/arm-dealers/ammunations/listing', 'Admin\ArmDearlerInventoriesController@showAmmunationsListing');

    //Domicile Verfifcation API for CFC
    Route::post('domicile-verification', 'Api\DomicileController@verifyDomicile');
    //Without Authorization
    //City Guide
    Route::get('city-guides-v2', 'Api\CityGuideController@webIndex')->name('cityGuides.index');
    Route::get('city-guides-mega-parks', 'Api\CityGuideController@megaParks')->name('cityGuides.mega-parks');
    Route::get('city-guides-weekly-bazars', 'Api\CityGuideController@weeklyBazars')->name('cityGuides.weekly-bazars');
    Route::get('psl-updates', 'Api\CityGuideController@pslUpdates')->name('psl.updates');
    //Side Menus
    Route::get('privacy-policy', function () {
        return view('privacy_policy');
    });
    Route::get('privacy-policy-quetta', function () {
        return view('privacy_policy_pakistanapp');
    });
    Route::get('bus-route', function () {
        return view('bus_routes');
    });
    Route::get('about_us', function () {
        return view('about_us');
    });
    Route::get('prevent-notice', function () {
        return view('prevent-notice');
    });
    Route::get('about_us_quetta', function () {
        return view('about_us_quetta');
    });

    //Appointments
    Route::get('get-appointment', function () {
        $user = auth('api')->user();
        //$user = true;
        if (!empty($user)) {
            $user_id = $user->id;
            $types = CustomTypeAppointment::where('status', 'Active')->get()->all();
            return view('custom_appointment.create', compact('types', 'user_id'));
        } else {
            abort('403');
        }
    });
    Route::get('get-department/{id}', 'DepartmentAppointmentController@getDepartment');
    Route::get('get-service/{id}', 'ServiceAppointmentController@getService');
    Route::get('get-location/{id}', 'IntervalAppointmentController@getLocation');
    Route::get('get-slot/{service_id}/{location_id}', 'IntervalAppointmentController@jsonGetSlot');
    Route::post('custom-appointment/store', 'IntervalAppointmentController@slotBook');
    Route::get('custom-appointment/listing', 'IntervalAppointmentController@getAppointmentList');




    //For Quetta UC
    Route::get('getUC/{id}', 'Admin\Library\LocationController@getUC');
    //For Quetta Tehsil
    Route::get('getTehsil/{id}', 'Admin\Library\LocationController@getTehsil');


    Route::get('teams', 'Admin\TeamController@webView');
    Route::get('user-manual', function () {
        return view('user_manual');
    });
    Route::get('help-desk', function () {
        return view('helpdesk');
    });
    //without authorization ends
    Route::get('close/web_view', function () {
        abort('403');
    });

    Route::get('darust-dam', function () {
        return view('darust_dam.index');
    });


    //Signup
    Route::get('custom-signup', function () {
        $provinces = Location::query()
            ->whereNull('parent_id')
            ->where('is_active', true)
            ->pluck('name', 'id');
        return view('signup', compact('provinces'));
    });
    Route::get('signup-get-districts/{id}', 'Admin\ProfileController@getDistricts');
    Route::get('signup-get-tehsils/{id}', 'Admin\ProfileController@getTehsils');
    //Register
    Route::group(['prefix' => 'auth-v2'], function () {
        Route::post('custom/register', 'Api\V2\AuthController@webviewRegister');
        Route::get('get-verify-code/{user_id}', 'Api\V2\AuthController@verifyCode');
    });

    //Local Certificate
    Route::post('create-local-certificate', 'Admin\ProfileController@createLocalCertificate');
    Route::get("local/list", "Admin\LocalCertificateController@indexWebView");

    //Verification
    Route::any('verify-code', 'Api\V2\AuthController@webVerify');
    Route::any('resend-code', 'Api\V2\AuthController@webResend');
    //Reset Password
    Route::post('reset-password', 'Api\V2\AuthController@webResetPassword');

    //Verification New Page (FOR ANDROID USER )
    Route::get('custom/verify-user', function () {
        $data = "verification";
        return view('forgot_password', compact('data'));
    });

    //Forgot Password
    Route::get('custom/forgot-password', function () {
        $data = "forget_password";
        return view('forgot_password', compact('data'));
    });
    Route::any('forgotPassword', 'Api\V2\AuthController@webForgotPassword')->name('password.forget');
    //Forgot Password and Signup ends
    //Change Password
    Route::get('custom/change-password', 'Admin\ProfileController@changePassword');
    Route::post('profile/password/update', 'Admin\ProfileController@updateWebPassword');

    Route::get('test', 'Api\TestController@run');

    Route::get('/banners', 'Admin\AppBannerController@getBanners');

    //NEW CODE
    Route::group(['middleware' => 'api'], function () {
        Route::group(['middleware' => 'auth:api'], function () {
            Route::any('/dashboard-details', 'Admin\AppDashboardController@getDetails');
            Route::any('/menu', 'Admin\AppMenuController@getMenu');
        });
    });

    Route::post('add-details', 'GarbageController@postDetails');
    Route::get('garbage-details', 'GarbageController@detailsList');
    Route::get('get-detail', 'GarbageController@detailsStatus');


    // Domicile and Arms Details
    // TODO: need to make ajax calls for all the below routes
    Route::post("arms/detail", "Admin\ProfileController@getArmsLicenseDetails");
    Route::post("domicile/detail", "Admin\ProfileController@getProfile");
    // Queries
    Route::get("queries/form/{id}", "Admin\QueryController@show");
    Route::post("general/query/store", "Admin\QueryController@store");
    Route::get("queries/form/history/{id}", "Admin\QueryController@showHistory");

    Route::get('token-tax-listing', 'Api\AssessmentController@tokenTaxListing');
    Route::get('registration-listing', 'Api\AssessmentController@registrationListing');
    Route::get('transfer-listing', 'Api\AssessmentController@transferListing');



    Route::post('token-tax-listing/detail', 'Api\AssessmentController@tokenTaxListingDetail');
    Route::get('general/verification', 'Admin\DomicileController@verificationDetails')->name('verification.index');

    // PITB General Verification
    Route::get('general-verification', 'Admin\DomicileController@generalVerification')->name('general.verification.index');
    //E-Police
    Route::get('license-verification', 'Admin\DomicileController@licenseVerification')->name('license.verification.index');
    Route::get('license-verification-v2', 'Admin\DomicileController@licenseVerificationV2')->name('license.verification-v2.index');
    Route::get('police/queries/form/{type}', 'Admin\PoliceController@showGeneralForm');
    Route::get('showAllIncident', 'Admin\PoliceController@showAllIncident');
    Route::get('showPoliceStation', 'Admin\PoliceController@showPoliceStation');
    Route::post('custom/report-incident', 'Admin\PoliceController@store');
    Route::get('police/queries/list', 'Admin\PoliceController@getHistory');
    //E-Police Ends

    //Web View Court Cases
    Route::get('/web/court-cases', 'Admin\CourtCaseController@webIndex');
    Route::get('/web/court-cases/pdf', 'Admin\CourtCaseController@pdfView');

    //fishery
    Route::get('/fishery/web/create', 'Admin\FisheryController@createWeb');
    Route::post('/fishery/web/store', 'Admin\FisheryController@webStore');
    Route::get('/fishery/web/list', 'Admin\FisheryController@webList');
    Route::post('/fishery/web/detail/{id}', 'Admin\FisheryController@webDetail');
    Route::post('/fishery/update/detail/{id}', 'Admin\FisheryController@fishery');

    //IDP
    Route::post('/custom/idp', 'Admin\IdpController@customSave');
    Route::get('idpWebView', 'Admin\IdpController@showWebView');
    Route::get('idpWebList', 'Admin\IdpController@indexWebView');
    //IDP detail page link with domicile Details page

    // birth certificate
    Route::group(['prefix' => 'birth_certificate'], function () {
        Route::post('/', 'Api\BirthCertificateController@store')->name('birth-certificate.store');
        Route::get('/', 'Api\BirthCertificateController@index')->name('birth-certificate.index');
        Route::get('/detail/{id}', 'Api\BirthCertificateController@detail')->name('birth-certificate.detail');
        //options
        Route::get('/options', 'Api\BirthCertificateOptionsController@index');

        Route::get('birthCertificateWebView', 'Admin\BirthCertificateController@showWebView');
        Route::post('storebirthCertificateWebView', 'Admin\BirthCertificateController@storeWebView')->name("bc.storeWebView");
        Route::get('birthCertificateWebList', 'Admin\BirthCertificateController@indexWebView');
        Route::get("birthCertificateWebList/detail", "Admin\BirthCertificateController@getProfile");
        Route::post("birthCertificateWebList/approvedbyuser", "Admin\BirthCertificateController@approvedByUser");
    });

    //FARD
    Route::post('/custom/fard', 'Admin\FardController@customSave');
    Route::get('fardWebView', 'Admin\FardController@showWebView');
    Route::get('fardWebList', 'Admin\FardController@indexWebView');
    Route::post('/custom/fard/detail', 'Admin\FardController@listingDetail');

    //Police Verification
    Route::get('police/custom/verification/{category}', 'Admin\PoliceController@showVerificationFrom');
    Route::post('police/custom/verification', 'Admin\PoliceController@showVerificationDetail');

    //Quetta Verification
    Route::get('police/quetta/verification', 'Admin\PoliceController@showQuettaPoliceVerificationFrom');
    Route::post('police/quetta/verification', 'Admin\PoliceController@showQuettaPoiliceVerificationDetail');

    //Quetta Tracking
    Route::get('police/quetta/tracking', 'Admin\PoliceController@showQuettaPoliceTrackingFrom');
    Route::post('police/quetta/tracking', 'Admin\PoliceController@showQuettaPoliceTrackingDetail');

    //Quetta Police Verification
    Route::get('driving-license/quetta/verification', 'Admin\PoliceController@showQuettaVerificationFrom');
    Route::post('driving-license/quetta/verification', 'Admin\PoliceController@showQuettaVerificationDetail');

    //Quetta Traffic Verification
    Route::get('driving-license/quetta/verification', 'Admin\PoliceController@showQuettaVerificationFrom');
    Route::post('driving-license/quetta/verification', 'Admin\PoliceController@showQuettaVerificationDetail');

    //Quetta Crime Report
    //Register Complaint
    Route::get('quetta/register/crime-report', 'Admin\PoliceController@registerComplainForm');
    Route::post('quetta/register/crime-report', 'Admin\PoliceController@registerComplaint');


    Route::get('quetta/crime-report', 'Admin\PoliceController@getComplainForm');
    Route::post('quetta/crime-report', 'Admin\PoliceController@complainFormDetails');


    //Library
    Route::get('/library/create/membership', 'Admin\Library\MembershipController@createWeb');
    //Route::post('/library/save/membership', 'Admin\Library\MembershipController@storeWeb');
    Route::post('/library/save/membership', 'Admin\Library\MembershipController@customApiStore');
    Route::get('/library/listing/membership', 'Admin\Library\MembershipController@webIndexListing');
    Route::post('/library/listing/membership/{id}', 'Admin\Library\MembershipController@webDetail');
    Route::post('/library/update/membership/{id}', 'Admin\Library\MembershipController@library');

    //library Ajax
    Route::get('/select2-category-ajax', 'Admin\Library\BookController@dataAjax');
    //Library Book
    Route::get('/library/search/book', 'Admin\Library\BookController@searchBook');
    Route::post('/library/search/book', 'Admin\Library\BookController@searchBookRequest');
    Route::post('/request/book', 'Admin\Library\BookController@bookRequest');
    Route::get('/myrequests/book', 'Admin\Library\BookController@myRequests');
    // Notifications
    Route::get('notice', 'Admin\NotificationController@indexWeb');
    Route::get('notice/{notification}', 'Admin\NotificationController@showWeb');

    // Profile data
    Route::get('getProfile/{cnic}', 'Admin\ProfileController@getPersonalData');
    Route::post('custom-update-personal-info/{user}', 'Admin\ProfileController@customUpdateUserInfo');
    Route::post('custom-update-image/{user}', 'Admin\ProfileController@profileImage');

    // Form documents and post requests
    Route::post('update-personal-info', 'Admin\ProfileController@updatePersonalInfo');
    Route::post('update-contact-info', 'Admin\ProfileController@updateContactInfo');
    Route::post('create-user-domicile', 'Admin\ProfileController@createDomicile');
    Route::post('create-user-idp', 'Admin\ProfileController@createIDP');
    Route::post('create-user-cpr', 'Admin\ProfileController@createCrp');
    Route::post('create-arms', 'Admin\ProfileController@createArmLicense');
    Route::post('create-arm-dealer', 'Admin\ArmDealersController@createArmDealer');
    Route::post('arms-dealer-documents', 'Admin\ArmDealersController@arm_dealer_docs');

    Route::post('app-documents', 'Admin\ProfileController@applicationDocuments');
    Route::post('app-documents/cpr', 'Admin\ProfileController@cprDocuments');
    Route::post('arms-documents', 'Admin\ProfileController@armsLicenseDocuments');


    //ISB FOOD AUTHORITY
    Route::post('create-user-food-authority', 'Admin\IsbFoodAuthoritiesController@store');
    Route::post('app-documents/food-authority', 'Admin\IsbFoodAuthoritiesController@storeDocument');
    Route::get("isb-food-authority/list", "Admin\IsbFoodAuthoritiesController@listing");
    Route::get("isb-food-authority/team/list", "Admin\IsbFoodAuthoritiesController@listingForTeam");
    Route::post("isb-food-authority/detail", "Admin\IsbFoodAuthoritiesController@details");
    Route::post("isb-food-authority/doc/update/detail/{application_id}", "Admin\IsbFoodAuthoritiesController@application");
    Route::post("isb-food-authority/update/detail/team", "Admin\IsbFoodAuthoritiesController@updateStatusByTeam");
    Route::post("isb-food-authority/verification", "Admin\IsbFoodAuthoritiesController@verify");
    Route::get('isb-food-authority/verify', function () {
        $data = "verification";
        return view('isb_food_authority.web_views.verify_license', compact('data'));
    });



    Route::get('/library/search/book', 'Admin\Library\BookController@searchBook');
    Route::post("application/update/detail/{application_id}", "Admin\DomicileController@application");

    // Domicile and Arms Listing
    Route::get("domicile/list", "Admin\DomicileController@indexWebView");
    Route::get("arms/list", "Admin\ArmController@armsListing");

    // Profile Reg
    Route::get('profile-reg/{type?}/{location_id?}', 'Admin\ProfileController@getProfileForm');

    Route::get('my-profile-v2', 'Admin\ProfileController@getProfile');

    Route::group(['prefix' => 'admin-access'], function () {
        Route::get('atta-distributor', function () {
            try {
                $user = auth('api')->user();
                if ($user) {
                    if ($user->hasRole('Atta Distributor')) {
                        $header = request()->header('Authorization', '');
                        $rand_str = auth('api')->getToken();
                        if (Str::startsWith($header, 'Bearer ')) {
                            $rand_str = Str::substr($header, 7);
                        }

                        return view('atta_check_distributor', compact('user', 'rand_str'));
                    } else {
                        return view('general_error');
                    }
                } else {
                    // return view('excise.clear-cache');
                    return view('general_error');
                }
            } catch (Exception $ex) {
                return $ex;
            }

            /*$user = User::query()->find(1);
            $rand_str = '';*/
        });
        Route::post('/post-atta-distributor', 'Api\QueryController@postAttaDetails');
        Route::post('/post-check-atta-distributor', 'Api\QueryController@postCheckAttaDetails');
    });
    Route::group(['prefix' => 'excise'], function () {
        Route::get('token-tax', function () {
            // return view('excise.maintenance_error');
            try {
                $user = auth('api')->user();
                if ($user) {
                    $header = request()->header('Authorization', '');
                    $rand_str = auth('api')->getToken();
                    if (Str::startsWith($header, 'Bearer ')) {
                        $rand_str = Str::substr($header, 7);
                    }

                    return view('excise.tokentax', compact('user', 'rand_str'));
                } else {

                    return view('excise.clear-cache');
                }
            } catch (Exception $ex) {
                return view('excise.clear-cache');
            }

            /*$user = User::query()->find(1);
            $rand_str = '';*/
        });
        Route::post('/token-tax-detail', 'Api\TokenTaxAssessmentController@showTokenDetails');
        Route::post('/post-token-tax', 'Api\TokenTaxAssessmentController@postTokenTax');
        //Route::any('/onelink/payment/create/{id}', 'Api\OneLinkController@createPayment');
        Route::post('/onelink/get-details', 'Api\OneLinkController@getDetails');
        //Route::get('/pay/{assessment}', 'Api\TokenTaxEsahulatController@show')->name('token_tax.pay');
        Route::post('/esahulat/get-details', 'Api\TokenTaxEsahulatController@getDetails');
    });

    Route::get('/excise/challan-form', function () {
        /*$user = User::query()->find(641);
        $rand_str = '';*/
        // return view('excise.maintenance_error');
        try {
            $user = auth('api')->user();
            if ($user) {
                $header = request()->header('Authorization', '');
                $rand_str = auth('api')->getToken();
                if (Str::startsWith($header, 'Bearer ')) {
                    $rand_str = Str::substr($header, 7);
                }

                return view('excise.transfer_registration', compact('user', 'rand_str'));
            } else {
                return view('excise.clear-cache');
            }
        } catch (\Exception $ex) {
            return view('excise.clear-cache');
        }
    });

    Route::get('/excise/challan-form-transfer', function () {
        /*$user = User::query()->find(641);
        $rand_str = '';*/
        // return view('excise.maintenance_error');
        try {
            $user = auth('api')->user();
            if ($user) {
                $header = request()->header('Authorization', '');
                $rand_str = auth('api')->getToken();
                if (Str::startsWith($header, 'Bearer ')) {
                    $rand_str = Str::substr($header, 7);
                }

                return view('excise.transfer_challan', compact('user', 'rand_str'));
            } else {
                return view('excise.clear-cache');
            }
        } catch (\Exception $ex) {
            return view('excise.clear-cache');
        }
    });

    Route::get('/excise/excise-challan-form', function () {
        /*$user = User::query()->find(641);
        $rand_str = '';*/
        // return view('excise.maintenance_error');
        try {
            $user = auth('api')->user();
            if ($user) {
                $header = request()->header('Authorization', '');
                $rand_str = auth('api')->getToken();
                if (Str::startsWith($header, 'Bearer ')) {
                    $rand_str = Str::substr($header, 7);
                }

                return view('excise.excise_challan', compact('user', 'rand_str'));
            } else {
                return view('excise.clear-cache');
            }
        } catch (\Exception $ex) {
            return view('excise.clear-cache');
        }
    });

    Route::post('/excise/challan/details', 'Api\ExciseController@showChallanDetails')->name('excise.challan.details');
    Route::any("/excise/challan/get-reg-transfer-details", "Api\ExciseController@createPayment")->name("excise.challan.get-details");
    Route::any("/excise/challan/create-payment", "Api\ExciseController@createPayment")->name("excise.challan.createpayment");
    Route::any("/excise/challan", "Api\ExciseController@challan")->name("excise.challan");
    Route::any("/excise/challan/pay", "Api\ExciseController@pay")->name("excise.challan.pay");
    Route::post('/excise/onelink/reg-transfer-details', 'Api\OneLinkController@getTransferRegDetails');
    Route::any("/excise/tokentaxchallan", "Api\ExciseController@tokenTaxChallan")->name("excise.tokentaxchallan");
    Route::post("/excise/challan/get-status", "Api\ExciseController@exciseChallanStatus");

    //FBISE
    Route::get('/fbise/get-form', 'Api\FbiseController@getSearchForm');
    Route::post('/fbise/search', 'Api\FbiseController@postSearchForm');
    //ITP Services
    Route::get('/itp/get-search-form', 'Api\ITPController@getDrivingLicenseVerification');
    Route::post('/itp/search', 'Api\ITPController@postDrivingLicenseVerification');
    Route::get('/itp/get-echallan-search-form', 'Api\ITPController@getEChallanForm');
    Route::post('/itp/echallan-search', 'Api\ITPController@postEChallanForm');
    Route::get('/itp/get-traffic-challan-form', 'Api\ITPController@getTrafficChallanForm');
    Route::post('/itp/traffic-challan-search', 'Api\ITPController@postTrafficChallanForm');
    //Verifications
    Route::get('general/form/verification/{id}', 'Admin\DomicileController@showVerificationFrom')->name('verification.show');

    Route::get('/get-cities', 'Admin\AppCityController@getAppCities');

    Route::group(['middleware' => 'api'], function () {

        Route::group(['prefix' => 'auth'], function () {
            Route::post('login', 'Api\AuthController@login');
            Route::post('register', 'Api\AuthController@register');
            Route::post('forgot-password', 'Api\AuthController@forgotPassword');
            Route::post('reset-password', 'Api\AuthController@resetPassword');
            Route::post('verify', 'Api\AuthController@verify'); /*->name('api.verification.verify')->middleware('signed');*/
            Route::post('resend', 'Api\AuthController@resend');
            Route::put('update/{user}', 'Api\AuthController@update');

            Route::group(['middleware' => 'auth:api'], function () {
                Route::post('logout', 'Api\AuthController@logout');
                Route::post('refresh', 'Api\AuthController@refresh');
                Route::get('me', 'Api\AuthController@me');
                Route::get('nadra-verified', 'Api\AuthController@checkNadraVerified');
                Route::post('change-password', 'Api\AuthController@changePassword');
            });
        });

        Route::group(['middleware' => 'auth:api'], function () {

            Route::post('esahulat/paymentCancel', 'Api\PaymentCancellationController@destroy')->name('e-sahulat.paymentCancel');

            Route::post('users/delete', 'Admin\UsersController@deleteUser')->name('users.get.delete');
            // profile
            Route::group(['prefix' => 'profile'], function () {
                Route::get('/', 'Api\ProfileController@show');
                Route::put('/{user}', 'Api\ProfileController@update')->middleware(['is_nadra_verified_user', 'is_nadra_verified_applicant']);
                Route::post('/image', 'Api\ProfileController@profileImage');
            });

            Route::get('my-profile', 'Admin\ProfileController@getProfile');

            // appointments
            Route::group(['prefix' => 'appointments'], function () {
                Route::post('/', 'Api\AppointmentController@store')->name('appointments.store');
                Route::get('/', 'Api\AppointmentController@index')->name('appointments.index');
                Route::get('/{appointment}', 'Api\AppointmentController@show')->name('appointments.show');
            });


            // services
            Route::group(['prefix' => 'services'/*, 'middleware' => 'is_nadra_verified'*/], function () {
                Route::get('/', 'Api\ServiceController@index')->name('services.index');
            });

            Route::group(['prefix' => 'services'], function () {
                Route::get('/generic-service', 'Api\ServiceController@getGenericService')->name('services.generic_service');
            });

            // domicile
            Route::group(['prefix' => 'domicile'], function () {
                Route::get('/', 'Api\DomicileController@index')->name('domicile.list')->middleware(['is_nadra_verified_user', 'is_nadra_verified_applicant']);
                Route::get('/detail/{id}', 'Api\DomicileController@detail')->name('domicile.detail');
                Route::post('/v2', 'Api\DomicileController@store')->name('domicile.store')->middleware(['is_nadra_verified_user', 'is_nadra_verified_applicant']);
                Route::put('/{application}', 'Api\DomicileController@update')->name('domicile.update');
                Route::get('all/detail/{cnic}', 'Api\DomicileController@adllDetail')->name('all.domicile.detail');
            });

            // ISB Food Authority
            Route::group(['prefix' => 'isb-food-authority'], function () {
                //Route::post('/', 'Api\IdpController@store')->name('idp.store')->middleware(['is_nadra_verified_user', 'is_nadra_verified_applicant']);

                Route::put('/{application}', 'Api\IdpController@update')->name('isb.food.update');
                Route::get('/', 'Api\IdpController@index')->name('isb.food.list');
                Route::get('/detail/{id}', 'Api\IdpController@detail')->name('isb.food.detail');
            });


            // idp
            Route::group(['prefix' => 'idp'], function () {
                //Route::post('/', 'Api\IdpController@store')->name('idp.store')->middleware(['is_nadra_verified_user', 'is_nadra_verified_applicant']);

                Route::put('/{application}', 'Api\IdpController@update')->name('idp.update');
                Route::get('/', 'Api\IdpController@index')->name('idp.list');
                Route::get('/detail/{id}', 'Api\IdpController@detail')->name('idp.detail');
            });

            // excise and taxation
            Route::post('/excise-new-registration ', 'Api\ExciseNewRegistrationController@store')->name('excise_new.store');
            Route::post('/excise-transfer', 'Api\ExciseTransferController@store')->name('excise_transfer.store');

            // payment for internal app
            Route::group(['prefix' => 'e-sahulat'], function () {
                Route::get('/{application}', 'Api\EsahulatController@show')->name('e-sahulat.show');
            });

            Route::group(['prefix' => 'fishery'], function () {
                Route::get('/categories', 'Api\FisheryController@categories');
                Route::post('/', 'Api\FisheryController@store');
                Route::get('/', 'Api\FisheryController@index');
                Route::get('/detail/{id}', 'Api\FisheryController@detail');
                Route::get('/settings', 'Api\FisheryController@settings');
            });

            // verifications
            Route::group(['prefix' => 'verification'], function () {
                Route::get('/', 'Api\VerificationController@index')->name('verification.index');
            });

            // notifications
            Route::post('notifications', 'Api\NotificationController@store')->name('notifications.store');

            // land certificate
            Route::group(['prefix' => 'land_certificate'], function () {
                Route::post('/', 'Api\LandCertificateController@store')->name('land-certificate.store');
                Route::get('/', 'Api\LandCertificateController@index')->name('land-certificate.index');
                Route::get('/detail/{id}', 'Api\LandCertificateController@detail')->name('land-certificate.detail');
            });

            // death certificate
            Route::group(['prefix' => 'death_certificate'], function () {
                Route::post('/', 'Api\DeathCertificateController@store')->name('death-certificate.store');
                Route::get('/', 'Api\DeathCertificateController@index')->name('death-certificate.index');
                Route::get('/detail/{id}', 'Api\DeathCertificateController@detail')->name('death-certificate.detail');
                //options
                Route::get('/options', 'Api\DeathCertificateOptionsController@index');
            });

            Route::group(['prefix' => 'library'], function () {
                Route::get('/membership/categories', 'Api\Library\MembershipController@categories');
                Route::post('/membership/', 'Api\Library\MembershipController@store');
                Route::get('/membership/', 'Api\Library\MembershipController@index');
                Route::get('/membership/detail/{id}', 'Api\Library\MembershipController@detail');
            });

            Route::group(['prefix' => 'library', 'namespace' => 'Api\Library'], function () {

                Route::group(['prefix' => 'book'], function () {
                    Route::get('/', 'BookController@index')->name('library.book');
                    Route::get('/filters', 'BookController@filters')->name('library.book.filters');
                    Route::post('/request', 'BookController@request')->name('library.book.request');
                    Route::get('/request', 'BookController@myRequests')->name('library.book.my.request');
                });

                Route::group(['prefix' => 'membership'], function () {
                    Route::get('/categories', 'MembershipController@categories');
                    Route::post('/', 'MembershipController@store');
                    Route::get('/', 'MembershipController@index');
                    Route::get('/detail/{id}', 'MembershipController@detail');
                });
            });

            Route::post('/documents/death/{death_certificate_id}', 'Api\DocumentController@death');
            Route::post('/documents/birth/{birth_certificate_id}', 'Api\DocumentController@birth');
            Route::post('/documents/domicile/{application_id}', 'Api\DocumentController@application');
            Route::post('/documents/idp/{application_id}', 'Api\DocumentController@application');
            Route::post('/documents/fishery/{application_id}', 'Api\DocumentController@fishery');
            Route::post('/documents/library/membership/{application_id}', 'Api\DocumentController@library');


            // police verification
            Route::group(['prefix' => 'police'], function () {

                // general listings
                Route::get('/service-centers', 'Api\PoliceGeneralController@centers')->name('police.service-centers.show');
                Route::get('/stations', 'Api\PoliceGeneralController@policeStations')->name('police.stations.show');
                Route::get('/incident-types', 'Api\PoliceGeneralController@incidentTypes')->name('police.incident-types.show');

                // verifications
                Route::get('/verification', 'Api\PoliceVerificationController@show')->name('police.verification.show');

                // smart ticketing
                Route::get('/smart-ticketing', 'Api\PoliceSmartTicketingController@index')->name('police.smart-ticketing.index');
                Route::post('/smart-ticketing', 'Api\PoliceSmartTicketingController@store')->name('police.smart-ticketing.store');

                // incident
                Route::get('/report-incident', 'Api\PoliceIncidentReportController@index')->name('police.incident-report.index');
                Route::post('/report-incident', 'Api\PoliceIncidentReportController@store')->name('police.incident-report.store');
            });

            // excise token tax
            Route::group(['prefix' => 'excise'], function () {

                Route::get('/assessment', 'Api\TokenTaxAssessmentController@index')->name('token-tax.index');
                Route::get('/pay/{assessment}', 'Api\TokenTaxEsahulatController@show')->name('token-tax.pay');
            });

            Route::get('get-districts/{id}', 'Admin\ProfileController@getDistricts');
            Route::get('get-tehsils/{id}', 'Admin\ProfileController@getTehsils');


            // General
            Route::get('countries', 'Api\CountryController@index')->name('countries.index');
            Route::get('states', 'Api\StateController@index')->name('states.index');
            Route::get('cities', 'Api\CityController@index')->name('cities.index');

            Route::any('/onelink/payment/create/{id}', 'Api\OneLinkController@createPayment');

            Route::get('/query', 'Api\QueryController@query');
            Route::get('/lightup', 'Api\QueryController@lightup');
            Route::post('/lightup', 'Api\QueryController@createLightup');
        });

        /* Esahulat Routes */
        Route::group(['middleware' => 'is_esahulat'], function () {

            // payment for nadra
            Route::group(['prefix' => 'esahulat'], function () {
                Route::get('/getFeeDetail', 'Api\EsahulatController@showByTrackingID')->name('e-sahulat.showByTrackingID');
                Route::post('/paymentInformation', 'Api\EsahulatController@PayByTrackingID')->name('e-sahulat.PayByTrackingID');
                Route::get('/paymentStatus', 'Api\PaymentStatusController@show')->name('e-sahulat.paymentStatus');
                Route::post('/paymentCancel', 'Api\PaymentCancellationController@destroy')->name('e-sahulat.paymentCancel');
            });
        });

        // notifications
        Route::get('notifications', 'Api\NotificationController@index')->name('notifications.index');
        Route::get('notifications/{notification}', 'Api\NotificationController@show')->name('notifications.show');

        // events
        Route::get('events', 'Api\EventController@index')->name('events.index');
        Route::get('events/{event}', 'Api\EventController@show')->name('events.show');

        Route::get('events-v2', 'Api\EventController@webView')->name('events-v2.index');

        // court cases
        Route::get('court-cases', 'Api\CourtCaseController@index')->name('court-cases.index');

        // city guides
        Route::get('city-guides', 'Api\CityGuideController@index')->name('cityGuides.index');
        Route::get('city-guides/{organization}', 'Api\CityGuideController@show')->name('cityGuides.show');

        // team
        Route::get('team', 'Api\TeamController@index')->name('team.index');

        // app queries
        Route::post('query', 'Api\QueryController@store')->name('query.store');

        // corona complaints
        Route::post('corona-complaints', 'Api\CoronaComplaintController@store')->name('corona-complaints.store');

        // featured services
        Route::group(['prefix' => 'featured-services'], function () {
            Route::get('/', 'Api\FeaturedServiceController@index')->name('featured services.index');
        });

        Route::group(['prefix' => 'generic-services'], function () {
            Route::get('/', 'Api\GenericServiceController@index')->name('generic_services.index');
        });

        // auth v2 api
        Route::group(['prefix' => 'auth-v2'], function () {
            Route::post('register', 'Api\V2\AuthController@register');
            Route::post('forgot-password', 'Api\V2\AuthController@forgotPassword');
            Route::post('reset-password', 'Api\V2\AuthController@resetPassword');
            Route::post('verify', 'Api\V2\AuthController@verify');
            Route::post('resend', 'Api\V2\AuthController@resend');
            Route::put('update/{user}', 'Api\V2\AuthController@update');
        });

        Route::get('/assessments/{application_type_id?}', 'Api\AssessmentController@index');
        Route::get('/tokentax-list/{user_id}', 'Api\AssessmentController@tokenTaxList');

        Route::any('/onelink/inquiry', 'Api\OneLinkController@inquiry');
        Route::any('/onelink/pay', 'Api\OneLinkController@pay');
        Route::any('/onelink/wsdl', 'Api\OneLinkController@wsdl')->name('onelink.wsdl');
        //Route::any('/onelink?wsdl', 'Api\OneLinkController@wsdl')->name('onelink.wsdl');

        Route::any('/onelink/BillInquiry', 'Api\OneLinkController@BillInquiry');
        Route::any('/onelink/BillPayment', 'Api\OneLinkController@BillPayment');
        Route::any('/onelink', 'Api\OneLinkController@index')->name('onelink.transaction');
    });

    Route::post('idp/', 'Api\IdpController@save')->name('idp.store')->middleware(['is_nadra_verified_user', 'is_nadra_verified_applicant']);

    Route::get('assessment/tokentax/pdf/{assessment_id}/{download?}', 'Api\AssessmentController@pdf')->name('assessment.tokentax.pdf');
    Route::get('/assessment/tokentax/view/{assessment_id}', 'Api\AssessmentController@view');


    Route::get('/application-types', 'Api\AssessmentController@applicationTypes');
    Route::get('/query/categories', 'Api\QueryController@getQueryCategories');
    Route::get('/query/business-types', 'Api\QueryController@businessTypes');

    Route::get("/app-backgrounds", "Admin\SettingsController@getAppBackgrounds")->name("app.backgrounds");
    Route::get('/statistics', 'Admin\DashboardController@print')->name('statistics');

    Route::get("/statistics/detail", "Admin\DashboardController@statistics")->name("dashboard.statistics.detail");
    Route::any("/complaint-form", "Api\QueryController@remote")->name("complaint.form");

    Route::any("/excise/smart-card", "Api\ExciseController@smartCard")->name("excise.smartcard");
    //Route::any("/excise/{target}", "Api\ExciseController@post")->name("excise.post")->where("target", "php$");
    Route::any("/excise/REQCARD.php", "Api\ExciseController@postsmartCardStatus");
    Route::any("/excise/registration", "Api\ExciseController@registration")->name("excise.registration");


    Route::any("/locations", "Api\LocationController@index");
});


// ->middleware('auth:api')
Route::prefix('v2')->middleware('auth:api')->group(function () {

    Route::prefix('oneinformation')->group(function () {
        Route::get('/show', [OneInformationController::class, 'showWebView']);
    });

    Route::any('/dashboard-details', 'Admin\AppDashboardController@getDetails4V2');

    Route::prefix('dropdowns')->group(function () {
        Route::get('/{type}', [DropdownController::class, 'getData']);
        Route::get('/districts/by-province', [DropdownController::class, 'getDistrictsByProvince']);
        Route::get('/tehsils/by-district', [DropdownController::class, 'getTehsilsByDistrict']);
    });

    Route::get('token-tax-listing', 'Api\AssessmentController@tokenTaxListing');
    Route::get('registration-listing', 'Api\AssessmentController@registrationListing');
    Route::get('transfer-listing', 'Api\AssessmentController@transferListing');
});
